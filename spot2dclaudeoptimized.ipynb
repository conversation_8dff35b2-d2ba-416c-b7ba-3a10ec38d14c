# Utility: Filter out spot IDs above 254 and generate accurate GT heatmaps

import numpy as np

import tifffile

from skimage.measure import regionprops, label



def filter_mask_and_ids(mask, max_id=254):
    """
    Filter out any spot IDs above max_id (default 254 for 8-bit masks).
    Returns a filtered mask and a list of valid spot IDs.
    """
    mask_filtered = mask.copy()
    mask_filtered[mask_filtered > max_id] = 0
    valid_ids = np.unique(mask_filtered)
    valid_ids = valid_ids[(valid_ids > 0) & (valid_ids <= max_id)]
    return mask_filtered, valid_ids



def get_centroids_and_sizes(mask, valid_ids):
    """
    For each valid spot ID, compute centroid and real area (not just equivalent diameter).
    Returns centroids (row, col), areas, and binary masks for each spot.
    """
    centroids = []
    areas = []
    spot_masks = []
    for iid in valid_ids:
        spot_mask = (mask == iid).astype(np.uint8)
        props = regionprops(spot_mask)
        if props:
            prop = props[0]
            centroids.append(prop.centroid)  # (row, col)
            areas.append(prop.area)
            spot_masks.append(spot_mask)
    return centroids, areas, spot_masks



def generate_gt_heatmap_from_masks(mask_shape, spot_masks):
    """
    Generate a GT heatmap where each spot is represented by its real binary mask (not Gaussian).
    """
    gt_heatmap = np.zeros(mask_shape, dtype=np.float32)
    for spot_mask in spot_masks:
        gt_heatmap = np.maximum(gt_heatmap, spot_mask.astype(np.float32))
    return gt_heatmap



# Example usage:

# mask = tifffile.imread(mask_path)

# mask_filtered, valid_ids = filter_mask_and_ids(mask)

# centroids, areas, spot_masks = get_centroids_and_sizes(mask_filtered, valid_ids)

# gt_heatmap = generate_gt_heatmap_from_masks(mask.shape, spot_masks)


# Example: Filter mask, extract centroids/areas, and generate accurate GT heatmap

import matplotlib.pyplot as plt



mask_path = "/mnt/d/Users/<USER>/FISH_spots/2d/image_raw/large spots/masks/synthetic_000596.tif"  # Example path

mask = tifffile.imread(mask_path)



# Filter out IDs above 254

mask_filtered, valid_ids = filter_mask_and_ids(mask)

print(f"Valid spot IDs (≤254): {valid_ids}")



# Get centroids, areas, and spot masks

centroids, areas, spot_masks = get_centroids_and_sizes(mask_filtered, valid_ids)

print(f"Found {len(centroids)} valid spots.")



# Generate accurate GT heatmap

gt_heatmap = generate_gt_heatmap_from_masks(mask.shape, spot_masks)



# Visualize

fig, axes = plt.subplots(1, 3, figsize=(15, 5))

axes[0].imshow(mask, cmap='nipy_spectral')

axes[0].set_title('Original Mask')

axes[1].imshow(mask_filtered, cmap='nipy_spectral')

axes[1].set_title('Filtered Mask (≤254)')

axes[2].imshow(gt_heatmap, cmap='hot')

axes[2].set_title('Accurate GT Heatmap (real spot size)')

plt.show()

import gc
gc.collect() # Forces garbage collection

import torch
torch.cuda.empty_cache()

import os

def rename_files(directory):
    # Iterate over all the files in the directory
    for filename in os.listdir(directory):
        # Check if the file starts with 'mask_'
        if filename.startswith('image_'):
            # Create the new filename by replacing 'mask_' with 'synthetic_'
            new_filename = filename.replace('image_', 'synthetic_', 1)

            # Create the full file paths
            old_file = os.path.join(directory, filename)
            new_file = os.path.join(directory, new_filename)

            # Rename the file
            os.rename(old_file, new_file)
            print(f'Renamed: {filename} to {new_filename}')

# Specify the directory containing the files
directory = '/mnt/d/Users/<USER>/FISH_spots/2d/synthetic/images/'  # Replace with the actual path to your directory

# Call the function to rename the files
rename_files(directory)

import matplotlib.pyplot as plt
import numpy as np
import tifffile
import cv2
import torch
from torch.utils.data import Dataset
from skimage.measure import regionprops
from scipy.spatial.distance import cdist
from scipy.ndimage import distance_transform_edt

class AdaptiveSpotDataset(Dataset):
    def __init__(self, image_paths, mask_paths, transform=None, patch_size=128, sparse_threshold=0.01):
        assert len(image_paths) == len(mask_paths)
        self.image_paths = image_paths
        self.mask_paths = mask_paths
        self.transform = transform
        self.patch_size = patch_size
        self.sparse_threshold = sparse_threshold
        self.weights = self._calculate_weights()

    def _calculate_weights(self):
        weights = []
        for mp in self.mask_paths:
            try:
                m = tifffile.imread(mp)
                num_inst = max(0, len(np.unique(m)) - 1)
                area = m.shape[0] * m.shape[1]
                density = num_inst * (self.patch_size ** 2) / (area + 1e-8)
                weights.append(1.0 / (density + self.sparse_threshold))
            except Exception as e:
                print(f"Error calculating weights: {e}")
                weights.append(1.0)
        w = torch.DoubleTensor(weights)
        return w / w.sum() * len(w)

    def _get_centroids_and_sizes(self, mask):
        centroids = []
        spot_sizes = []
        props = regionprops(mask.astype(int))

        for prop in props:
            cy, cx = prop.centroid
            centroids.append([cy, cx])
            spot_sizes.append(prop.equivalent_diameter)

        return centroids, spot_sizes

    def _adaptive_heatmap_generation(self, mask, centroids, spot_sizes):
        h, w = mask.shape
        y_coords, x_coords = np.meshgrid(np.arange(h), np.arange(w), indexing='ij')

        fine_heatmap = np.zeros_like(mask, dtype=np.float32)
        medium_heatmap = np.zeros_like(mask, dtype=np.float32)
        coarse_heatmap = np.zeros_like(mask, dtype=np.float32)

        for (cy, cx), size in zip(centroids, spot_sizes):
            sigma = np.clip(size * 0.4, 0.8, 10.0)
            g = np.exp(-((y_coords - cy) ** 2 + (x_coords - cx) ** 2) / (2 * sigma ** 2))

            if size <= 3:
                fine_heatmap = np.clip(fine_heatmap + g, 0, 1)
            elif size <= 8:
                medium_heatmap = np.clip(medium_heatmap + g, 0, 1)
            else:
                coarse_heatmap = np.clip(coarse_heatmap + g, 0, 1)

        return [fine_heatmap, medium_heatmap, coarse_heatmap]

    def _generate_distance_transform(self, mask):
        semantic = (mask > 0).astype(np.uint8)
        distance = distance_transform_edt(semantic)
        max_possible_distance = np.sqrt(self.patch_size ** 2 + self.patch_size ** 2)
        distance = np.clip(distance / max_possible_distance, 0, 1)
        return distance.astype(np.float32)

    def _generate_flow_field(self, mask, centroids):
        h, w = mask.shape
        y_coords, x_coords = np.meshgrid(np.arange(h), np.arange(w), indexing='ij')

        flow_y = np.zeros((h, w), dtype=np.float32)
        flow_x = np.zeros((h, w), dtype=np.float32)

        if len(centroids) == 0:
            return np.stack([flow_y, flow_x], axis=0)

        centroids_arr = np.array(centroids)
        spot_mask = (mask > 0)

        if not spot_mask.any():
            return np.stack([flow_y, flow_x], axis=0)

        spot_pixels = np.column_stack(np.where(spot_mask))
        distances = cdist(spot_pixels, centroids_arr)
        nearest_centroid_idx = np.argmin(distances, axis=1)

        for i, (py, px) in enumerate(spot_pixels):
            nearest_centroid = centroids_arr[nearest_centroid_idx[i]]
            cy, cx = nearest_centroid

            dy = cy - py
            dx = cx - px
            norm = np.sqrt(dy**2 + dx**2) + 1e-8

            flow_y[py, px] = dy / norm
            flow_x[py, px] = dx / norm

        return np.stack([flow_y, flow_x], axis=0)

    def __len__(self):
        return len(self.image_paths)

    def __getitem__(self, idx):
        empty_img = torch.zeros((1, self.patch_size, self.patch_size), dtype=torch.float32)
        empty_mask = torch.zeros((6, self.patch_size, self.patch_size), dtype=torch.float32)
        empty_flow = torch.zeros((2, self.patch_size, self.patch_size), dtype=torch.float32)
        empty_conf = torch.zeros((1, self.patch_size, self.patch_size), dtype=torch.float32)

        for attempt in range(10):
            try:
                img = tifffile.imread(self.image_paths[idx]).astype(np.float32)
                msk = tifffile.imread(self.mask_paths[idx]).astype(np.int32)
                img /= (img.max() + 1e-8)
                img = np.clip(img, 0, 1)
                H, W = img.shape[:2]
                x0 = np.random.randint(0, max(0, H - self.patch_size))
                y0 = np.random.randint(0, max(0, W - self.patch_size))

                patch_img = img[x0:x0+self.patch_size, y0:y0+self.patch_size]
                patch_msk = msk[x0:x0+self.patch_size, y0:y0+self.patch_size]

                ph = self.patch_size - patch_img.shape[0]
                pw = self.patch_size - patch_img.shape[1]
                if ph > 0 or pw > 0:
                    patch_img = np.pad(patch_img, ((0, ph), (0, pw)), 'reflect')
                    patch_msk = np.pad(patch_msk, ((0, ph), (0, pw)), 'constant')

                centroids, spot_sizes = self._get_centroids_and_sizes(patch_msk)

                heatmaps = self._adaptive_heatmap_generation(patch_msk, centroids, spot_sizes)
                distance_map = self._generate_distance_transform(patch_msk)
                flow_field = self._generate_flow_field(patch_msk, centroids)

                sem = (patch_msk > 0).astype(np.float32)
                bnd = np.zeros_like(sem, dtype=np.float32)
                for iid in np.unique(patch_msk):
                    if iid == 0: continue
                    im = (patch_msk == iid).astype(np.uint8)
                    kernel = np.ones((2,2), np.uint8)
                    er = cv2.erode(im, kernel, iterations=1)
                    bnd += ((im - er) > 0).astype(np.float32)
                bnd = np.clip(bnd, 0, 1)

                mask_stack = np.stack([sem, bnd, distance_map] + heatmaps, axis=0)

                if self.transform is not None:
                    img_u8 = (patch_img * 255).astype(np.uint8)
                    mask_hwc = np.moveaxis(mask_stack, 0, -1)
                    flow_hwc = np.moveaxis(flow_field, 0, -1)

                    aug = self.transform(image=img_u8, mask=mask_hwc, flow=flow_hwc)
                    patch_img = aug['image'].astype(np.float32) / 255.0
                    mask_stack = np.moveaxis(aug['mask'], -1, 0).astype(np.float32)
                    flow_field = np.moveaxis(aug.get('flow', flow_hwc), -1, 0).astype(np.float32)

                    mask_stack[0] = (mask_stack[0] > 0.5).astype(np.float32)
                    mask_stack[1] = (mask_stack[1] > 0.5).astype(np.float32)

                img_t = torch.from_numpy(patch_img).unsqueeze(0)
                msk_t = torch.from_numpy(mask_stack)
                flow_t = torch.from_numpy(flow_field)

                semantic = mask_stack[0]
                spot_density = (semantic > 0).sum() / (self.patch_size ** 2)

                if spot_density < 0.05:
                    confidence = (semantic > 0).astype(np.float32)
                elif spot_density > 0.3:
                    confidence = np.ones_like(semantic, dtype=np.float32)
                    confidence[mask_stack[1] > 0] *= 2.0
                else:
                    confidence = np.ones_like(semantic, dtype=np.float32)

                confidence_t = torch.from_numpy(confidence)

                if len(centroids) == 0:
                    continue

                return img_t, msk_t, flow_t, confidence_t

            except Exception as e:
                print(f"Error in dataset __getitem__ attempt {attempt}: {e}")
                continue

        return empty_img, empty_mask, empty_flow, empty_conf

def visualize_mask_and_centroids(mask, centroids):
    plt.figure(figsize=(5, 5))
    plt.imshow(mask, cmap='nipy_spectral')
    plt.scatter([c[1] for c in centroids], [c[0] for c in centroids], c='red', s=10)
    plt.title('Mask with Centroids')
    plt.axis('off')
    plt.show()

def visualize_heatmaps(heatmaps):
    fig, axes = plt.subplots(1, 3, figsize=(15, 5))
    axes[0].imshow(heatmaps[0], cmap='hot')
    axes[0].set_title('Fine Heatmap')
    axes[0].axis('off')
    axes[1].imshow(heatmaps[1], cmap='hot')
    axes[1].set_title('Medium Heatmap')
    axes[1].axis('off')
    axes[2].imshow(heatmaps[2], cmap='hot')
    axes[2].set_title('Coarse Heatmap')
    axes[2].axis('off')
    plt.show()


# Example usage
image_paths = ["/mnt/d/Users/<USER>/FISH_spots/2d/image_raw/large spots/images/synthetic_000596.tif" , "/mnt/d/Users/<USER>/FISH_spots/2d/image_raw/large spots/images/synthetic_000597.tif"] # Replace with actual paths
mask_paths = ["/mnt/d/Users/<USER>/FISH_spots/2d/image_raw/large spots/masks/synthetic_000596.tif", "/mnt/d/Users/<USER>/FISH_spots/2d/image_raw/large spots/masks/synthetic_000597.tif"]  # Replace with actual paths


# Create an instance of the AdaptiveSpotDataset class
dataset = AdaptiveSpotDataset(image_paths, mask_paths)

# Test the heatmap generation
for idx in range(len(dataset)):
    img_t, msk_t, flow_t, confidence_t = dataset[idx]
    mask = msk_t[0].numpy()
    centroids, spot_sizes = dataset._get_centroids_and_sizes(mask)
    heatmaps = dataset._adaptive_heatmap_generation(mask, centroids, spot_sizes)

    visualize_mask_and_centroids(mask, centroids)
    visualize_heatmaps(heatmaps)

import numpy as np
import cv2
import tifffile
import matplotlib.pyplot as plt
from skimage.measure import regionprops, label
from skimage.feature import peak_local_max

class AdaptiveSpotDataset:
    def __init__(self, patch_size=128):
        self.patch_size = patch_size

    @staticmethod
    def filter_mask_and_ids(mask, max_id=254):
        mask_filtered = mask.copy()
        mask_filtered[mask_filtered > max_id] = 0
        valid_ids = np.unique(mask_filtered)
        valid_ids = valid_ids[(valid_ids > 0) & (valid_ids <= max_id)]
        return mask_filtered, valid_ids

    @staticmethod
    def get_centroids_and_sizes(mask, valid_ids):
        centroids = []
        areas = []
        spot_masks = []
        for iid in valid_ids:
            spot_mask = (mask == iid).astype(np.uint8)
            props = regionprops(spot_mask)
            if props:
                prop = props[0]
                centroids.append(prop.centroid)  # (row, col)
                areas.append(prop.area)
                spot_masks.append(spot_mask)
        return centroids, areas, spot_masks

    @staticmethod
    def generate_gt_heatmap_from_masks(mask_shape, spot_masks):
        gt_heatmap = np.zeros(mask_shape, dtype=np.float32)
        for spot_mask in spot_masks:
            gt_heatmap = np.maximum(gt_heatmap, spot_mask.astype(np.float32))
        return gt_heatmap

    def _adaptive_heatmap_generation(self, mask, centroids, spot_sizes):
        h, w = mask.shape
        y_coords, x_coords = np.meshgrid(np.arange(h), np.arange(w), indexing='ij')
        combined_heatmap = np.zeros_like(mask, dtype=np.float32)
        print(f"Generating heatmap for {len(centroids)} spots")
        print(f"Spot sizes: {spot_sizes}")
        for i, ((cy, cx), size) in enumerate(zip(centroids, spot_sizes)):
            if size <= 3:
                sigma = max(1.0, size * 0.5)
                category = "fine"
            elif size <= 8:
                sigma = max(2.0, size * 0.4)
                category = "medium"
            else:
                sigma = max(3.0, size * 0.3)
                category = "coarse"
            gaussian = np.exp(-((y_coords - cy) ** 2 + (x_coords - cx) ** 2) / (2 * sigma ** 2))
            combined_heatmap = np.maximum(combined_heatmap, gaussian)
            print(f"Spot {i+1}: center=({cy:.1f}, {cx:.1f}), size={size:.1f}, sigma={sigma:.1f}, category={category}")
        return combined_heatmap

    def _generate_separate_heatmaps(self, mask, centroids, spot_sizes):
        h, w = mask.shape
        y_coords, x_coords = np.meshgrid(np.arange(h), np.arange(w), indexing='ij')
        fine_heatmap = np.zeros_like(mask, dtype=np.float32)
        medium_heatmap = np.zeros_like(mask, dtype=np.float32)
        coarse_heatmap = np.zeros_like(mask, dtype=np.float32)
        for i, ((cy, cx), size) in enumerate(zip(centroids, spot_sizes)):
            if size <= 3:
                sigma = max(1.0, size * 0.5)
                gaussian = np.exp(-((y_coords - cy) ** 2 + (x_coords - cx) ** 2) / (2 * sigma ** 2))
                fine_heatmap = np.maximum(fine_heatmap, gaussian)
                category = "fine"
            elif size <= 8:
                sigma = max(2.0, size * 0.4)
                gaussian = np.exp(-((y_coords - cy) ** 2 + (x_coords - cx) ** 2) / (2 * sigma ** 2))
                medium_heatmap = np.maximum(medium_heatmap, gaussian)
                category = "medium"
            else:
                sigma = max(3.0, size * 0.3)
                gaussian = np.exp(-((y_coords - cy) ** 2 + (x_coords - cx) ** 2) / (2 * sigma ** 2))
                coarse_heatmap = np.maximum(coarse_heatmap, gaussian)
                category = "coarse"
            if category == "coarse":
                print(f"  COARSE SPOT {i+1}: center=({cy:.1f}, {cx:.1f}), size={size:.1f}, sigma={sigma:.1f}")
                print(f"    Gaussian max value: {gaussian.max():.3f}")
                print(f"    Gaussian spread: {np.sum(gaussian > 0.1)} pixels > 0.1")
        if spot_sizes:
            print(f"Spot size distribution:")
            print(f"  Fine spots (≤3): {sum(1 for s in spot_sizes if s <= 3)}")
            print(f"  Medium spots (3-8): {sum(1 for s in spot_sizes if 3 < s <= 8)}")
            print(f"  Coarse spots (>8): {sum(1 for s in spot_sizes if s > 8)}")
            print(f"  Size range: {min(spot_sizes):.1f} - {max(spot_sizes):.1f}")
            print(f"  Large spots (>15): {[i for i, s in enumerate(spot_sizes) if s > 15]}")
        return [fine_heatmap, medium_heatmap, coarse_heatmap]

def test_heatmap_generation_fixed(image_paths, mask_paths):
    dataset = AdaptiveSpotDataset()
    for i, (img_path, mask_path) in enumerate(zip(image_paths, mask_paths)):
        print(f"\n=== Processing Image {i+1} ===")
        image = tifffile.imread(img_path)
        mask = tifffile.imread(mask_path)
        mask_filtered, valid_ids = dataset.filter_mask_and_ids(mask)
        print(f"Image shape: {image.shape}")
        print(f"Mask shape: {mask.shape}")
        print(f"Valid spot IDs (≤254): {valid_ids}")
        image_normalized = image.astype(np.float32)
        image_normalized /= (image_normalized.max() + 1e-8)
        image_normalized = np.clip(image_normalized, 0, 1)
        centroids, areas, spot_masks = dataset.get_centroids_and_sizes(mask_filtered, valid_ids)
        print(f"Found {len(centroids)} valid spots.")
        gt_heatmap = dataset.generate_gt_heatmap_from_masks(mask.shape, spot_masks)
        spot_sizes = [np.sqrt(a/np.pi)*2 for a in areas]  # Diameter from area for Gaussian comparison
        combined_heatmap = dataset._adaptive_heatmap_generation(mask, centroids, spot_sizes)
        separate_heatmaps = dataset._generate_separate_heatmaps(mask, centroids, spot_sizes)
        fig, axes = plt.subplots(2, 4, figsize=(20, 10))
        axes[0, 0].imshow(image, cmap='gray')
        axes[0, 0].set_title('Original Image')
        axes[0, 0].axis('off')
        axes[0, 1].imshow(image_normalized, cmap='gray')
        axes[0, 1].set_title('Normalized Image')
        axes[0, 1].axis('off')
        axes[0, 2].imshow(mask_filtered, cmap='nipy_spectral')
        if centroids:
            axes[0, 2].scatter([c[1] for c in centroids], [c[0] for c in centroids], c='red', s=20, marker='x')
        axes[0, 2].set_title(f'Filtered Mask with {len(centroids)} Centroids')
        axes[0, 2].axis('off')
        axes[0, 3].imshow(gt_heatmap, cmap='hot')
        axes[0, 3].set_title('Accurate GT Heatmap (real spot size)')
        axes[0, 3].axis('off')
        heatmap_titles = ['Fine Heatmap', 'Medium Heatmap', 'Coarse Heatmap']
        for j, (heatmap, title) in enumerate(zip(separate_heatmaps, heatmap_titles)):
            axes[1, j].imshow(heatmap, cmap='hot')
            axes[1, j].set_title(f'{title} (max: {heatmap.max():.3f})')
            axes[1, j].axis('off')
        all_combined = np.maximum.reduce(separate_heatmaps)
        axes[1, 3].imshow(all_combined, cmap='hot')
        axes[1, 3].set_title('All Heatmaps Combined (Gaussian)')
        axes[1, 3].axis('off')
        plt.tight_layout()
        plt.show()
        print(f"\nDetailed Analysis:")
        print(f"  Spots in mask: {len(valid_ids)}")
        print(f"  Centroids found: {len(centroids)}")
        if areas:
            large_spots = [(i, size, centroids[i]) for i, size in enumerate(spot_sizes) if size > 15]
            if large_spots:
                print(f"\nLARGE SPOTS (>15 pixels) - these create the bright coarse heatmap:")
                for i, size, (cy, cx) in large_spots:
                    print(f"  Spot {i+1}: size={size:.1f}, center=({cy:.1f}, {cx:.1f})")
                    spot_id = valid_ids[i] if i < len(valid_ids) else f"unknown_{i}"
                    print(f"    Spot ID in mask: {spot_id}")
                    if i < len(valid_ids):
                        spot_mask = (mask_filtered == valid_ids[i])
                        actual_area = np.sum(spot_mask)
                        print(f"    Actual pixel area: {actual_area}")
                        print(f"    Diameter: {size:.1f}")
                        margin = 20
                        y_min = max(0, int(cy - margin))
                        y_max = min(mask.shape[0], int(cy + margin))
                        x_min = max(0, int(cx - margin))
                        x_max = min(mask.shape[1], int(cx + margin))
                        spot_region = mask_filtered[y_min:y_max, x_min:x_max]
                        print(f"    Region around spot: {spot_region.shape}")
                        print(f"    Unique values in region: {np.unique(spot_region)}")
        very_large_spots = [s for s in spot_sizes if s > 30]
        if very_large_spots:
            print(f"\nWARNING: Found {len(very_large_spots)} very large spots (>30 pixels)")
            print(f"These might be:")
            print(f"  - Merged/touching spots that should be separate")
            print(f"  - Annotation errors in the mask")
            print(f"  - Actual large biological structures")
            print(f"Sizes: {very_large_spots}")
        print(f"\nHeatmap Statistics:")
        for i, (heatmap, name) in enumerate(zip(separate_heatmaps, ['Fine', 'Medium', 'Coarse'])):
            print(f"  {name}: max={heatmap.max():.3f}, non-zero pixels={np.sum(heatmap > 0.01)}")
        print(f"  Combined heatmap max: {combined_heatmap.max():.3f}")
        print(f"  Combined heatmap non-zero pixels: {np.sum(combined_heatmap > 0.01)}")


# Example usage
if __name__ == "__main__":
    # Your image and mask paths
    image_paths = [
        "/mnt/d/Users/<USER>/FISH_spots/2d/image_raw/large spots/images/synthetic_000596.tif",
        "/mnt/d/Users/<USER>/FISH_spots/2d/image_raw/large spots/images/synthetic_000597.tif"
    ]
    
    mask_paths = [
        "/mnt/d/Users/<USER>/FISH_spots/2d/image_raw/large spots/masks/synthetic_000596.tif",
        "/mnt/d/Users/<USER>/FISH_spots/2d/image_raw/large spots/masks/synthetic_000597.tif"
    ]
    
    # Test the fixed heatmap generation
    test_heatmap_generation_fixed(image_paths, mask_paths)
    
    # Additional analysis for large spots
    print("\n" + "="*50)
    print("DETAILED LARGE SPOT ANALYSIS")
    print("="*50)
    
    # Load first image for detailed analysis
    mask = tifffile.imread(mask_paths[0])
    
    # Get centroids and sizes
    centroids = []
    spot_sizes = []
    unique_ids = np.unique(mask)
    unique_ids = unique_ids[unique_ids > 0]
    
    for iid in unique_ids:
        spot_mask = (mask == iid).astype(np.uint8)
        props = regionprops(spot_mask)
        if len(props) > 0:
            prop = props[0]
            cy, cx = prop.centroid
            centroids.append([cy, cx])
            spot_sizes.append(prop.equivalent_diameter)
    
    # Analyze large spots
    analyze_large_spots(mask, centroids, spot_sizes, threshold=15)

import numpy as np
import cv2
import tifffile
import matplotlib.pyplot as plt
from skimage.measure import regionprops
from skimage.feature import peak_local_max
from skimage.measure import label

class AdaptiveSpotDataset:
    def __init__(self, patch_size=128):
        self.patch_size = patch_size
    
    def _adaptive_heatmap_generation(self, mask, centroids, spot_sizes):
        """FIXED: Proper coordinate system and comprehensive spot detection"""
        h, w = mask.shape
        
        # FIXED: Use consistent meshgrid with indexing='ij' (row, col)
        y_coords, x_coords = np.meshgrid(np.arange(h), np.arange(w), indexing='ij')
        
        # FIXED: Create a single combined heatmap that includes ALL spots
        combined_heatmap = np.zeros_like(mask, dtype=np.float32)
        
        # Debug information
        print(f"Generating heatmap for {len(centroids)} spots")
        print(f"Spot sizes: {spot_sizes}")
        
        # Process each spot individually
        for i, ((cy, cx), size) in enumerate(zip(centroids, spot_sizes)):
            # FIXED: Improved sigma scaling with better size categorization
            if size <= 3:
                sigma = max(1.0, size * 0.5)  # Fine spots - smaller sigma
                category = "fine"
            elif size <= 8:
                sigma = max(2.0, size * 0.4)  # Medium spots
                category = "medium"
            else:
                sigma = max(3.0, size * 0.3)  # Coarse spots - larger sigma
                category = "coarse"
            
            # FIXED: Consistent coordinate usage - cy=row, cx=col
            gaussian = np.exp(-((y_coords - cy) ** 2 + (x_coords - cx) ** 2) / (2 * sigma ** 2))
            
            # FIXED: Add to combined heatmap (not separate heatmaps)
            combined_heatmap = np.maximum(combined_heatmap, gaussian)
            
            print(f"Spot {i+1}: center=({cy:.1f}, {cx:.1f}), size={size:.1f}, sigma={sigma:.1f}, category={category}")
        
        # FIXED: Return single heatmap that contains ALL spots
        return combined_heatmap
    
    def _generate_separate_heatmaps(self, mask, centroids, spot_sizes):
        """Generate separate heatmaps for fine, medium, and coarse spots"""
        h, w = mask.shape
        y_coords, x_coords = np.meshgrid(np.arange(h), np.arange(w), indexing='ij')
        
        fine_heatmap = np.zeros_like(mask, dtype=np.float32)
        medium_heatmap = np.zeros_like(mask, dtype=np.float32)
        coarse_heatmap = np.zeros_like(mask, dtype=np.float32)
        
        for i, ((cy, cx), size) in enumerate(zip(centroids, spot_sizes)):
            # Create Gaussian for this spot
            if size <= 3:
                sigma = max(1.0, size * 0.5)
                gaussian = np.exp(-((y_coords - cy) ** 2 + (x_coords - cx) ** 2) / (2 * sigma ** 2))
                fine_heatmap = np.maximum(fine_heatmap, gaussian)
                category = "fine"
            elif size <= 8:
                sigma = max(2.0, size * 0.4)
                gaussian = np.exp(-((y_coords - cy) ** 2 + (x_coords - cx) ** 2) / (2 * sigma ** 2))
                medium_heatmap = np.maximum(medium_heatmap, gaussian)
                category = "medium"
            else:
                sigma = max(3.0, size * 0.3)
                gaussian = np.exp(-((y_coords - cy) ** 2 + (x_coords - cx) ** 2) / (2 * sigma ** 2))
                coarse_heatmap = np.maximum(coarse_heatmap, gaussian)
                category = "coarse"
            
            # Debug: Print details for coarse spots
            if category == "coarse":
                print(f"  COARSE SPOT {i+1}: center=({cy:.1f}, {cx:.1f}), size={size:.1f}, sigma={sigma:.1f}")
                print(f"    Gaussian max value: {gaussian.max():.3f}")
                print(f"    Gaussian spread: {np.sum(gaussian > 0.1)} pixels > 0.1")
        
        # Debug: Print size distribution
        if spot_sizes:
            print(f"Spot size distribution:")
            print(f"  Fine spots (≤3): {sum(1 for s in spot_sizes if s <= 3)}")
            print(f"  Medium spots (3-8): {sum(1 for s in spot_sizes if 3 < s <= 8)}")
            print(f"  Coarse spots (>8): {sum(1 for s in spot_sizes if s > 8)}")
            print(f"  Size range: {min(spot_sizes):.1f} - {max(spot_sizes):.1f}")
            print(f"  Large spots (>15): {[i for i, s in enumerate(spot_sizes) if s > 15]}")
        
        return [fine_heatmap, medium_heatmap, coarse_heatmap]
    
    def _adaptive_heatmap_generation_original_fixed(self, mask, centroids, spot_sizes):
        """Fixed version of your original method - returns all 3 heatmaps but ensures ALL spots are included"""
        h, w = mask.shape
        
        # FIXED: Use consistent meshgrid with indexing='ij' (row, col)
        y_coords, x_coords = np.meshgrid(np.arange(h), np.arange(w), indexing='ij')
        
        # Size-adaptive heatmaps
        fine_heatmap = np.zeros_like(mask, dtype=np.float32)      # Small spots
        medium_heatmap = np.zeros_like(mask, dtype=np.float32)    # Medium spots  
        coarse_heatmap = np.zeros_like(mask, dtype=np.float32)    # Large spots
        
        for (cy, cx), size in zip(centroids, spot_sizes):
            # FIXED: Improved sigma scaling for better small spot handling
            sigma = np.clip(size * 0.4, 0.8, 10.0)  # More aggressive scaling
            
            # FIXED: Consistent coordinate usage - cy=row, cx=col
            g = np.exp(-((y_coords - cy) ** 2 + (x_coords - cx) ** 2) / (2 * sigma ** 2))
            
            # FIXED: Use np.maximum instead of np.clip for better separation
            if size <= 3:
                fine_heatmap = np.maximum(fine_heatmap, g)
            elif size <= 8:
                medium_heatmap = np.maximum(medium_heatmap, g)
            else:
                coarse_heatmap = np.maximum(coarse_heatmap, g)
        
        return [fine_heatmap, medium_heatmap, coarse_heatmap]

def test_heatmap_generation_fixed(image_paths, mask_paths):
    """Fixed test function with better debugging"""
    dataset = AdaptiveSpotDataset()
    
    for i, (img_path, mask_path) in enumerate(zip(image_paths, mask_paths)):
        print(f"\n=== Processing Image {i+1} ===")
        
        # Load the image and mask
        image = tifffile.imread(img_path)
        mask = tifffile.imread(mask_path)
        
        print(f"Image shape: {image.shape}")
        print(f"Mask shape: {mask.shape}")
        print(f"Unique mask values: {np.unique(mask)}")
        
        # Normalize the image
        image_normalized = image.astype(np.float32)
        image_normalized /= (image_normalized.max() + 1e-8)
        image_normalized = np.clip(image_normalized, 0, 1)
        
        # FIXED: Get centroids and spot sizes from the mask
        centroids = []
        spot_sizes = []
        unique_ids = np.unique(mask)
        unique_ids = unique_ids[unique_ids > 0]  # Remove background
        
        print(f"Found {len(unique_ids)} unique spot IDs")
        
        for iid in unique_ids:
            # Create binary mask for this spot
            spot_mask = (mask == iid).astype(np.uint8)
            
            # Get properties
            props = regionprops(spot_mask)
            
            if len(props) > 0:
                prop = props[0]
                cy, cx = prop.centroid  # Returns (row, col)
                centroids.append([cy, cx])
                spot_sizes.append(prop.equivalent_diameter)
                print(f"  Spot {iid}: centroid=({cy:.1f}, {cx:.1f}), size={prop.equivalent_diameter:.1f}")
            else:
                print(f"  Warning: No properties found for spot ID {iid}")
        
        print(f"Total processed spots: {len(centroids)}")
        
        # Generate combined heatmap
        combined_heatmap = dataset._adaptive_heatmap_generation(mask, centroids, spot_sizes)
        
        # Generate separate heatmaps
        separate_heatmaps = dataset._generate_separate_heatmaps(mask, centroids, spot_sizes)
        
        # Create visualization
        fig, axes = plt.subplots(2, 4, figsize=(20, 10))
        
        # Top row: Original analysis
        axes[0, 0].imshow(image, cmap='gray')
        axes[0, 0].set_title('Original Image')
        axes[0, 0].axis('off')
        
        axes[0, 1].imshow(image_normalized, cmap='gray')
        axes[0, 1].set_title('Normalized Image')
        axes[0, 1].axis('off')
        
        # Show mask with centroids
        axes[0, 2].imshow(mask, cmap='nipy_spectral')
        if centroids:
            axes[0, 2].scatter([c[1] for c in centroids], [c[0] for c in centroids], 
                             c='red', s=20, marker='x')
        axes[0, 2].set_title(f'Mask with {len(centroids)} Centroids')
        axes[0, 2].axis('off')
        
        # Show combined heatmap
        axes[0, 3].imshow(combined_heatmap, cmap='hot')
        axes[0, 3].set_title('Combined GT Heatmap')
        axes[0, 3].axis('off')
        
        # Bottom row: Separate heatmaps
        heatmap_titles = ['Fine Heatmap', 'Medium Heatmap', 'Coarse Heatmap']
        for j, (heatmap, title) in enumerate(zip(separate_heatmaps, heatmap_titles)):
            axes[1, j].imshow(heatmap, cmap='hot')
            axes[1, j].set_title(f'{title} (max: {heatmap.max():.3f})')
            axes[1, j].axis('off')
        
        # Show all heatmaps combined
        all_combined = np.maximum.reduce(separate_heatmaps)
        axes[1, 3].imshow(all_combined, cmap='hot')
        axes[1, 3].set_title('All Heatmaps Combined')
        axes[1, 3].axis('off')
        
        plt.tight_layout()
        plt.show()
        
        # ENHANCED Verification: Detailed analysis of large spots
        print(f"\nDetailed Analysis:")
        print(f"  Spots in mask: {len(unique_ids)}")
        print(f"  Centroids found: {len(centroids)}")
        
        if spot_sizes:
            large_spots = [(i, size, centroids[i]) for i, size in enumerate(spot_sizes) if size > 15]
            if large_spots:
                print(f"\nLARGE SPOTS (>15 pixels) - these create the bright coarse heatmap:")
                for i, size, (cy, cx) in large_spots:
                    print(f"  Spot {i+1}: size={size:.1f}, center=({cy:.1f}, {cx:.1f})")
                    
                    # Check what this spot looks like in the mask
                    spot_id = unique_ids[i] if i < len(unique_ids) else f"unknown_{i}"
                    print(f"    Spot ID in mask: {spot_id}")
                    
                    # Find the actual spot in the mask
                    if i < len(unique_ids):
                        spot_mask = (mask == unique_ids[i])
                        actual_area = np.sum(spot_mask)
                        print(f"    Actual pixel area: {actual_area}")
                        print(f"    Equivalent diameter: {size:.1f}")
                        
                        # Show a small region around this spot
                        margin = 20
                        y_min = max(0, int(cy - margin))
                        y_max = min(mask.shape[0], int(cy + margin))
                        x_min = max(0, int(cx - margin))
                        x_max = min(mask.shape[1], int(cx + margin))
                        
                        spot_region = mask[y_min:y_max, x_min:x_max]
                        print(f"    Region around spot: {spot_region.shape}")
                        print(f"    Unique values in region: {np.unique(spot_region)}")
        
        # Check for potential mask annotation issues
        very_large_spots = [s for s in spot_sizes if s > 30]
        if very_large_spots:
            print(f"\nWARNING: Found {len(very_large_spots)} very large spots (>30 pixels)")
            print(f"These might be:")
            print(f"  - Merged/touching spots that should be separate")
            print(f"  - Annotation errors in the mask")
            print(f"  - Actual large biological structures")
            print(f"Sizes: {very_large_spots}")
        
        print(f"\nHeatmap Statistics:")
        for i, (heatmap, name) in enumerate(zip(separate_heatmaps, ['Fine', 'Medium', 'Coarse'])):
            print(f"  {name}: max={heatmap.max():.3f}, non-zero pixels={np.sum(heatmap > 0.01)}")
        print(f"  Combined heatmap max: {combined_heatmap.max():.3f}")
        print(f"  Combined heatmap non-zero pixels: {np.sum(combined_heatmap > 0.01)}")

def analyze_large_spots(mask, centroids, spot_sizes, threshold=15):
    """Analyze and visualize large spots that might be causing issues"""
    large_spots = [(i, size, centroids[i]) for i, size in enumerate(spot_sizes) if size > threshold]
    
    if not large_spots:
        print(f"No spots larger than {threshold} pixels found.")
        return
    
    print(f"Found {len(large_spots)} large spots (>{threshold} pixels):")
    
    # Create visualization for each large spot
    for j, (i, size, (cy, cx)) in enumerate(large_spots):
        print(f"\nLarge Spot {j+1}:")
        print(f"  Index: {i}, Size: {size:.1f}, Center: ({cy:.1f}, {cx:.1f})")
        
        # Extract region around the spot
        margin = max(20, int(size))
        y_min = max(0, int(cy - margin))
        y_max = min(mask.shape[0], int(cy + margin))
        x_min = max(0, int(cx - margin))
        x_max = min(mask.shape[1], int(cx + margin))
        
        region = mask[y_min:y_max, x_min:x_max]
        
        # Find the spot ID
        spot_ids = np.unique(mask)
        spot_ids = spot_ids[spot_ids > 0]
        
        if i < len(spot_ids):
            spot_id = spot_ids[i]
            spot_mask = (mask == spot_id).astype(np.uint8)
            spot_region = spot_mask[y_min:y_max, x_min:x_max]
            
            # Show the region
            fig, axes = plt.subplots(1, 3, figsize=(15, 5))
            
            # Original region
            axes[0].imshow(region, cmap='nipy_spectral')
            axes[0].set_title(f'Mask Region (Spot ID: {spot_id})')
            axes[0].plot([cx - x_min], [cy - y_min], 'r+', markersize=10)
            
            # Binary spot mask
            axes[1].imshow(spot_region, cmap='gray')
            axes[1].set_title(f'Binary Spot Mask (Size: {size:.1f})')
            axes[1].plot([cx - x_min], [cy - y_min], 'r+', markersize=10)
            
            # Analyze spot shape
            from skimage.measure import regionprops
            props = regionprops(spot_region)
            if props:
                prop = props[0]
                axes[2].imshow(spot_region, cmap='gray')
                axes[2].set_title(f'Shape Analysis\nArea: {prop.area}, Eccentricity: {prop.eccentricity:.2f}')
                
                # Draw bounding box
                minr, minc, maxr, maxc = prop.bbox
                axes[2].add_patch(plt.Rectangle((minc, minr), maxc-minc, maxr-minr, 
                                              fill=False, edgecolor='red', linewidth=2))
                
                print(f"  Area: {prop.area} pixels")
                print(f"  Equivalent diameter: {prop.equivalent_diameter:.1f}")
                print(f"  Eccentricity: {prop.eccentricity:.2f} (0=circle, 1=line)")
                print(f"  Bounding box: {prop.bbox}")
                
                # Check if it might be multiple merged spots
                if prop.eccentricity > 0.8:
                    print(f"  WARNING: High eccentricity suggests this might be merged spots!")
                if prop.area > 100:
                    print(f"  WARNING: Very large area suggests possible annotation error!")
            
            plt.tight_layout()
            plt.show()

# Example usage
if __name__ == "__main__":
    # Your image and mask paths
    image_paths = [
        "/mnt/d/Users/<USER>/FISH_spots/2d/image_raw/large spots/images/synthetic_000596.tif",
        "/mnt/d/Users/<USER>/FISH_spots/2d/image_raw/large spots/images/synthetic_000597.tif"
    ]
    
    mask_paths = [
        "/mnt/d/Users/<USER>/FISH_spots/2d/image_raw/large spots/masks/synthetic_000596.tif",
        "/mnt/d/Users/<USER>/FISH_spots/2d/image_raw/large spots/masks/synthetic_000597.tif"
    ]
    
    # Test the fixed heatmap generation
    test_heatmap_generation_fixed(image_paths, mask_paths)
    
    # Additional analysis for large spots
    print("\n" + "="*50)
    print("DETAILED LARGE SPOT ANALYSIS")
    print("="*50)
    
    # Load first image for detailed analysis
    mask = tifffile.imread(mask_paths[0])
    
    # Get centroids and sizes
    centroids = []
    spot_sizes = []
    unique_ids = np.unique(mask)
    unique_ids = unique_ids[unique_ids > 0]
    
    for iid in unique_ids:
        spot_mask = (mask == iid).astype(np.uint8)
        props = regionprops(spot_mask)
        if len(props) > 0:
            prop = props[0]
            cy, cx = prop.centroid
            centroids.append([cy, cx])
            spot_sizes.append(prop.equivalent_diameter)
    
    # Analyze large spots
    analyze_large_spots(mask, centroids, spot_sizes, threshold=15)

import os
import glob
import random
import traceback
import cv2
import numpy as np
import tifffile
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader, WeightedRandomSampler
from sklearn.model_selection import train_test_split
from skimage.feature import peak_local_max
from skimage.measure import label, regionprops
from scipy.spatial.distance import cdist
from scipy.ndimage import distance_transform_edt
import albumentations as A
from albumentations.pytorch import ToTensorV2

class AdaptiveSpotDataset(Dataset):
    def __init__(self, image_paths, mask_paths, transform=None, patch_size=128, 
                 sparse_threshold=0.01):
        assert len(image_paths) == len(mask_paths)
        self.image_paths = image_paths
        self.mask_paths = mask_paths
        self.transform = transform
        self.patch_size = patch_size
        self.sparse_threshold = sparse_threshold
        self.weights = self._calculate_weights()

    def _calculate_weights(self):
        weights = []
        for mp in self.mask_paths:
            try:
                m = tifffile.imread(mp)
                num_inst = max(0, len(np.unique(m)) - 1)
                area = m.shape[0] * m.shape[1]
                density = num_inst * (self.patch_size ** 2) / (area + 1e-8)
                weights.append(1.0 / (density + self.sparse_threshold))
            except Exception:
                weights.append(1.0)
        w = torch.DoubleTensor(weights)
        return w / w.sum() * len(w)

    @staticmethod
    def filter_mask_and_ids(mask, max_id=254):
        mask_filtered = mask.copy()
        mask_filtered[mask_filtered > max_id] = 0
        valid_ids = np.unique(mask_filtered)
        valid_ids = valid_ids[(valid_ids > 0) & (valid_ids <= max_id)]
        return mask_filtered, valid_ids

    @staticmethod
    def get_centroids_and_sizes(mask, valid_ids):
        centroids = []
        areas = []
        spot_masks = []
        for iid in valid_ids:
            spot_mask = (mask == iid).astype(np.uint8)
            props = regionprops(spot_mask)
            if props:
                prop = props[0]
                centroids.append(prop.centroid)  # (row, col)
                areas.append(prop.area)
                spot_masks.append(spot_mask)
        return centroids, areas, spot_masks

    @staticmethod
    def generate_instance_mask(mask_shape, spot_masks):
        # Union of all spot masks (binary mask for all spots)
        instance_mask = np.zeros(mask_shape, dtype=np.float32)
        for spot_mask in spot_masks:
            instance_mask = np.maximum(instance_mask, spot_mask.astype(np.float32))
        return instance_mask

    def _adaptive_heatmap_generation(self, mask, centroids, spot_sizes):
        h, w = mask.shape
        y_coords, x_coords = np.meshgrid(np.arange(h), np.arange(w), indexing='ij')
        combined_heatmap = np.zeros_like(mask, dtype=np.float32)
        for i, ((cy, cx), size) in enumerate(zip(centroids, spot_sizes)):
            if size <= 3:
                sigma = max(1.0, size * 0.5)
            elif size <= 8:
                sigma = max(2.0, size * 0.4)
            else:
                sigma = max(3.0, size * 0.3)
            gaussian = np.exp(-((y_coords - cy) ** 2 + (x_coords - cx) ** 2) / (2 * sigma ** 2))
            combined_heatmap = np.maximum(combined_heatmap, gaussian)
        return combined_heatmap

    def _generate_distance_transform(self, mask):
        semantic = (mask > 0).astype(np.uint8)
        distance = distance_transform_edt(semantic)
        max_possible_distance = np.sqrt(self.patch_size ** 2 + self.patch_size ** 2)
        distance = np.clip(distance / max_possible_distance, 0, 1)
        return distance.astype(np.float32)

    def _generate_flow_field(self, mask, centroids):
        h, w = mask.shape
        y_coords, x_coords = np.meshgrid(np.arange(h), np.arange(w), indexing='ij')
        flow_y = np.zeros((h, w), dtype=np.float32)
        flow_x = np.zeros((h, w), dtype=np.float32)
        if len(centroids) == 0:
            return np.stack([flow_y, flow_x], axis=0)
        centroids_arr = np.array(centroids)
        spot_mask = (mask > 0)
        if not spot_mask.any():
            return np.stack([flow_y, flow_x], axis=0)
        spot_pixels = np.column_stack(np.where(spot_mask))
        from scipy.spatial.distance import cdist
        distances = cdist(spot_pixels, centroids_arr)
        nearest_centroid_idx = np.argmin(distances, axis=1)
        for i, (py, px) in enumerate(spot_pixels):
            nearest_centroid = centroids_arr[nearest_centroid_idx[i]]
            cy, cx = nearest_centroid
            dy = cy - py
            dx = cx - px
            norm = np.sqrt(dy**2 + dx**2) + 1e-8
            flow_y[py, px] = dy / norm
            flow_x[py, px] = dx / norm
        return np.stack([flow_y, flow_x], axis=0)

    def __len__(self):
        return len(self.image_paths)

    def __getitem__(self, idx):
        empty_img = torch.zeros((1, self.patch_size, self.patch_size), dtype=torch.float32)
        empty_mask = torch.zeros((5, self.patch_size, self.patch_size), dtype=torch.float32)
        empty_flow = torch.zeros((2, self.patch_size, self.patch_size), dtype=torch.float32)
        empty_conf = torch.zeros((1, self.patch_size, self.patch_size), dtype=torch.float32)
        for attempt in range(10):
            try:
                img = tifffile.imread(self.image_paths[idx]).astype(np.float32)
                msk = tifffile.imread(self.mask_paths[idx]).astype(np.int32)
                img /= (img.max() + 1e-8)
                img = np.clip(img, 0, 1)
                H, W = img.shape[:2]
                x0 = random.randint(0, max(0, H - self.patch_size))
                y0 = random.randint(0, max(0, W - self.patch_size))
                msk, valid_ids = self.filter_mask_and_ids(msk)
                if len(valid_ids) > 0:
                    iid = random.choice(valid_ids)
                    coords = np.argwhere(msk == iid)
                    cy, cx = coords.mean(axis=0).astype(int)
                    x0 = np.clip(cy - self.patch_size // 2, 0, H - self.patch_size)
                    y0 = np.clip(cx - self.patch_size // 2, 0, W - self.patch_size)
                patch_img = img[x0:x0+self.patch_size, y0:y0+self.patch_size]
                patch_msk = msk[x0:x0+self.patch_size, y0:y0+self.patch_size]
                ph = self.patch_size - patch_img.shape[0]
                pw = self.patch_size - patch_img.shape[1]
                if ph > 0 or pw > 0:
                    patch_img = np.pad(patch_img, ((0, ph), (0, pw)), 'reflect')
                    patch_msk = np.pad(patch_msk, ((0, ph), (0, pw)), 'constant')
                sem = (patch_msk > 0).astype(np.float32)
                bnd = np.zeros_like(sem, dtype=np.float32)
                for iid in np.unique(patch_msk):
                    if iid == 0: continue
                    im = (patch_msk == iid).astype(np.uint8)
                    kernel = np.ones((2,2), np.uint8)
                    er = cv2.erode(im, kernel, iterations=1)
                    bnd += ((im - er) > 0).astype(np.float32)
                bnd = np.clip(bnd, 0, 1)
                centroids, areas, spot_masks = self.get_centroids_and_sizes(patch_msk, np.unique(patch_msk)[np.unique(patch_msk) > 0])
                spot_sizes = [np.sqrt(a/np.pi)*2 for a in areas]  # Diameter from area for Gaussian
                instance_mask = self.generate_instance_mask(patch_msk.shape, spot_masks)
                gaussian_heatmap = self._adaptive_heatmap_generation(patch_msk, centroids, spot_sizes)
                distance_map = self._generate_distance_transform(patch_msk)
                flow_field = self._generate_flow_field(patch_msk, centroids)
                # Stack all target channels: [semantic, boundary, distance, instance mask, gaussian heatmap]
                mask_stack = np.stack([sem, bnd, distance_map, instance_mask, gaussian_heatmap], axis=0)
                if self.transform is not None:
                    img_u8 = (patch_img * 255).astype(np.uint8)
                    mask_hwc = np.moveaxis(mask_stack, 0, -1)
                    flow_hwc = np.moveaxis(flow_field, 0, -1)
                    aug = self.transform(image=img_u8, mask=mask_hwc, flow=flow_hwc)
                    patch_img = aug['image'].astype(np.float32) / 255.0
                    mask_stack = np.moveaxis(aug['mask'], -1, 0).astype(np.float32)
                    flow_field = np.moveaxis(aug.get('flow', flow_hwc), -1, 0).astype(np.float32)
                    mask_stack[0] = (mask_stack[0] > 0.5).astype(np.float32)
                    mask_stack[1] = (mask_stack[1] > 0.5).astype(np.float32)
                img_t = ToTensorV2()(image=patch_img)['image']
                msk_t = torch.from_numpy(mask_stack)
                flow_t = torch.from_numpy(flow_field)
                semantic = mask_stack[0]
                spot_density = (semantic > 0).sum() / (self.patch_size ** 2)
                if spot_density < 0.05:
                    confidence = (semantic > 0).astype(np.float32)
                elif spot_density > 0.3:
                    confidence = np.ones_like(semantic, dtype=np.float32)
                    confidence[mask_stack[1] > 0] *= 2.0
                else:
                    confidence = np.ones_like(semantic, dtype=np.float32)
                confidence_t = torch.from_numpy(confidence)
                if len(centroids) == 0:
                    continue
                return img_t, msk_t, flow_t, confidence_t
            except Exception as e:
                print(f"Error in dataset __getitem__ attempt {attempt}: {e}")
                continue
        return empty_img, empty_mask, empty_flow, empty_conf

###START HERE

# %% [markdown]
# # Optimized Spot Detection Model (2025)
#
# This implementation combines the latest advances in deep learning for microscopy image analysis.
# The model is optimized for both accuracy and speed, with special handling for sparse annotations.

# %%
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader, WeightedRandomSampler
from torchvision import transforms
import tifffile
import matplotlib.pyplot as plt
from sklearn.model_selection import train_test_split
from sklearn.metrics import precision_score, recall_score, f1_score, jaccard_score
import os
import glob
import time
import json
from functools import partial
from typing import List, Tuple, Dict, Optional
import warnings
import traceback
from skimage.transform import resize
import cv2


# Set random seed for reproducibility
torch.manual_seed(42)
np.random.seed(42)

# Suppress some warnings for cleaner output
warnings.filterwarnings('ignore', category=UserWarning)

# Check for GPU availability and configure
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
print(f"Using device: {device}")
torch.backends.cudnn.benchmark = True  # Enable cuDNN auto-tuner for faster operations

# Enable gradient checkpointing for memory efficiency
torch.utils.checkpoint.checkpoint = lambda f, *args: f(*args)  # Placeholder
GRADIENT_CHECKPOINTING = False






### UPDATED 7/11/2025
### UPDATED 7/11/2025
import os
import glob
import random
import traceback
import cv2
import numpy as np
import tifffile
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader, WeightedRandomSampler
from sklearn.model_selection import train_test_split
from skimage.feature import peak_local_max
from skimage.measure import label, regionprops
from scipy.spatial.distance import cdist
from scipy.ndimage import distance_transform_edt
import albumentations as A
from albumentations.pytorch import ToTensorV2

class AdaptiveSpotDataset(Dataset):
    """
    Dataset for spot detection and segmentation. Outputs:
      - Channel 0: semantic mask (binary, any spot)
      - Channel 1: boundary mask
      - Channel 2: distance transform
      - Channel 3: soft-blurred instance mask (for segmentation)
      - Channel 4: centroid map (for detection)
    """
    def __init__(self, image_paths, mask_paths, transform=None, patch_size=128, 
                 sparse_threshold=0.01, blur_sigma=0.2):
        assert len(image_paths) == len(mask_paths)
        self.image_paths = image_paths
        self.mask_paths = mask_paths
        self.transform = transform
        self.patch_size = patch_size
        self.sparse_threshold = sparse_threshold
        self.blur_sigma = blur_sigma
        self.weights = self._calculate_weights()

    def _calculate_weights(self):
        weights = []
        for mp in self.mask_paths:
            try:
                m = tifffile.imread(mp)
                num_inst = max(0, len(np.unique(m)) - 1)
                area = m.shape[0] * m.shape[1]
                density = num_inst * (self.patch_size ** 2) / (area + 1e-8)
                weights.append(1.0 / (density + self.sparse_threshold))
            except Exception:
                weights.append(1.0)
        w = torch.DoubleTensor(weights)
        return w / w.sum() * len(w)

    @staticmethod
    def get_valid_ids(mask):
        valid_ids = np.unique(mask)
        valid_ids = valid_ids[valid_ids > 0]
        return valid_ids

    @staticmethod
    def get_centroids_and_sizes(mask, valid_ids):
        centroids = []
        areas = []
        spot_masks = []
        for iid in valid_ids:
            spot_mask = (mask == iid).astype(np.uint8)
            props = regionprops(spot_mask)
            if props:
                prop = props[0]
                centroids.append(prop.centroid)  # (row, col)
                areas.append(prop.area)
                spot_masks.append(spot_mask)
        return centroids, areas, spot_masks

    @staticmethod
    def generate_soft_blurred_instance_mask(mask_shape, spot_masks, sigma=0.1):
        soft_mask = np.zeros(mask_shape, dtype=np.float32)
        for spot_mask in spot_masks:
            blurred = cv2.GaussianBlur(spot_mask.astype(np.float32), (0,0), sigma)
            soft_mask = np.maximum(soft_mask, blurred)
        if soft_mask.max() > 0:
            soft_mask = soft_mask / soft_mask.max()
        return soft_mask

    @staticmethod
    def generate_centroid_map(mask_shape, centroids):
        centroid_map = np.zeros(mask_shape, dtype=np.float32)
        for cy, cx in centroids:
            cy = int(round(cy))
            cx = int(round(cx))
            if 0 <= cy < mask_shape[0] and 0 <= cx < mask_shape[1]:
                centroid_map[cy, cx] = 1.0
        return centroid_map

    def _generate_distance_transform(self, mask):
        semantic = (mask > 0).astype(np.uint8)
        distance = distance_transform_edt(semantic)
        max_possible_distance = np.sqrt(self.patch_size ** 2 + self.patch_size ** 2)
        distance = np.clip(distance / max_possible_distance, 0, 1)
        return distance.astype(np.float32)

    def _generate_flow_field(self, mask, centroids):
        h, w = mask.shape
        y_coords, x_coords = np.meshgrid(np.arange(h), np.arange(w), indexing='ij')
        flow_y = np.zeros((h, w), dtype=np.float32)
        flow_x = np.zeros((h, w), dtype=np.float32)
        if len(centroids) == 0:
            return np.stack([flow_y, flow_x], axis=0)
        centroids_arr = np.array(centroids)
        spot_mask = (mask > 0)
        if not spot_mask.any():
            return np.stack([flow_y, flow_x], axis=0)
        spot_pixels = np.column_stack(np.where(spot_mask))
        from scipy.spatial.distance import cdist
        distances = cdist(spot_pixels, centroids_arr)
        nearest_centroid_idx = np.argmin(distances, axis=1)
        for i, (py, px) in enumerate(spot_pixels):
            nearest_centroid = centroids_arr[nearest_centroid_idx[i]]
            cy, cx = nearest_centroid
            dy = cy - py
            dx = cx - px
            norm = np.sqrt(dy**2 + dx**2) + 1e-8
            flow_y[py, px] = dy / norm
            flow_x[py, px] = dx / norm
        return np.stack([flow_y, flow_x], axis=0)

    def __len__(self):
        return len(self.image_paths)

    def __getitem__(self, idx):
        empty_img = torch.zeros((1, self.patch_size, self.patch_size), dtype=torch.float32)
        empty_mask = torch.zeros((5, self.patch_size, self.patch_size), dtype=torch.float32)
        empty_flow = torch.zeros((2, self.patch_size, self.patch_size), dtype=torch.float32)
        empty_conf = torch.zeros((1, self.patch_size, self.patch_size), dtype=torch.float32)
        for attempt in range(10):
            try:
                img = tifffile.imread(self.image_paths[idx]).astype(np.float32)
                msk = tifffile.imread(self.mask_paths[idx]).astype(np.int32)
                img /= (img.max() + 1e-8)
                img = np.clip(img, 0, 1)
                H, W = img.shape[:2]
                x0 = random.randint(0, max(0, H - self.patch_size))
                y0 = random.randint(0, max(0, W - self.patch_size))
                valid_ids = self.get_valid_ids(msk)
                if len(valid_ids) > 0:
                    iid = random.choice(valid_ids)
                    coords = np.argwhere(msk == iid)
                    cy, cx = coords.mean(axis=0).astype(int)
                    x0 = np.clip(cy - self.patch_size // 2, 0, H - self.patch_size)
                    y0 = np.clip(cx - self.patch_size // 2, 0, W - self.patch_size)
                patch_img = img[x0:x0+self.patch_size, y0:y0+self.patch_size]
                patch_msk = msk[x0:x0+self.patch_size, y0:y0+self.patch_size]
                ph = self.patch_size - patch_img.shape[0]
                pw = self.patch_size - patch_img.shape[1]
                if ph > 0 or pw > 0:
                    patch_img = np.pad(patch_img, ((0, ph), (0, pw)), 'reflect')
                    patch_msk = np.pad(patch_msk, ((0, ph), (0, pw)), 'constant')
                # FIXED: Create tighter semantic masks by eroding spot regions
                sem = np.zeros_like(patch_msk, dtype=np.float32)
                for iid in np.unique(patch_msk):
                    if iid == 0: continue
                    spot_mask = (patch_msk == iid).astype(np.uint8)
                    # Erode each spot to create tighter semantic region
                    kernel = np.ones((3,3), np.uint8)
                    eroded = cv2.erode(spot_mask, kernel, iterations=1)
                    # If erosion removes the spot completely, use original
                    if eroded.sum() > 0:
                        sem += eroded.astype(np.float32)
                    else:
                        sem += spot_mask.astype(np.float32)
                sem = np.clip(sem, 0, 1)
                bnd = np.zeros_like(sem, dtype=np.float32)
                for iid in np.unique(patch_msk):
                    if iid == 0: continue
                    im = (patch_msk == iid).astype(np.uint8)
                    kernel = np.ones((2,2), np.uint8)
                    er = cv2.erode(im, kernel, iterations=1)
                    bnd += ((im - er) > 0).astype(np.float32)
                bnd = np.clip(bnd, 0, 1)
                centroids, areas, spot_masks = self.get_centroids_and_sizes(
                    patch_msk, self.get_valid_ids(patch_msk))
                # Soft-blurred instance mask
                soft_instance_mask = self.generate_soft_blurred_instance_mask(
                    patch_msk.shape, spot_masks, sigma=self.blur_sigma)
                # Centroid map
                centroid_map = self.generate_centroid_map(patch_msk.shape, centroids)
                distance_map = self._generate_distance_transform(patch_msk)
                # Flow field (direction to centroid for each spot pixel)
                flow_field = self._generate_flow_field(patch_msk, centroids)
                # Stack all target channels: [semantic, boundary, distance, soft instance mask, centroid map]
                mask_stack = np.stack([sem, bnd, distance_map, soft_instance_mask, centroid_map], axis=0)
                if self.transform is not None:
                    img_u8 = (patch_img * 255).astype(np.uint8)
                    mask_hwc = np.moveaxis(mask_stack, 0, -1)
                    flow_hwc = np.moveaxis(flow_field, 0, -1)
                    aug = self.transform(image=img_u8, mask=mask_hwc, flow=flow_hwc)
                    patch_img = aug['image'].astype(np.float32) / 255.0
                    mask_stack = np.moveaxis(aug['mask'], -1, 0).astype(np.float32)
                    flow_field = np.moveaxis(aug.get('flow', flow_hwc), -1, 0).astype(np.float32)
                    mask_stack[0] = (mask_stack[0] > 0.5).astype(np.float32)
                    mask_stack[1] = (mask_stack[1] > 0.5).astype(np.float32)
                img_t = ToTensorV2()(image=patch_img)['image']
                msk_t = torch.from_numpy(mask_stack)
                flow_t = torch.from_numpy(flow_field)
                semantic = mask_stack[0]
                spot_density = (semantic > 0).sum() / (self.patch_size ** 2)
                if spot_density < 0.05:
                    confidence = (semantic > 0).astype(np.float32)
                elif spot_density > 0.3:
                    confidence = np.ones_like(semantic, dtype=np.float32)
                    confidence[mask_stack[1] > 0] *= 2.0
                else:
                    confidence = np.ones_like(semantic, dtype=np.float32)
                confidence_t = torch.from_numpy(confidence).unsqueeze(0)  # Shape [1, H, W]
                if len(centroids) == 0:
                    continue
                return img_t, msk_t, flow_t, confidence_t
            except Exception as e:
                print(f"Error in dataset __getitem__ attempt {attempt}: {e}")
                continue
        return empty_img, empty_mask, empty_flow, empty_conf





###UPDATED WITH FILTER:7/10/205
# import os
# import glob
# import random
# import traceback
# import cv2
# import numpy as np
# import tifffile
# import torch
# import torch.nn as nn
# import torch.nn.functional as F
# from torch.utils.data import Dataset, DataLoader, WeightedRandomSampler
# from sklearn.model_selection import train_test_split
# from skimage.feature import peak_local_max
# from skimage.measure import label, regionprops
# from scipy.spatial.distance import cdist
# from scipy.ndimage import distance_transform_edt
# import albumentations as A
# from albumentations.pytorch import ToTensorV2

# class AdaptiveSpotDataset(Dataset):
#     def __init__(self, image_paths, mask_paths, transform=None, patch_size=128, 
#                  sparse_threshold=0.01):
#         assert len(image_paths) == len(mask_paths)
#         self.image_paths = image_paths
#         self.mask_paths = mask_paths
#         self.transform = transform
#         self.patch_size = patch_size
#         self.sparse_threshold = sparse_threshold
#         self.weights = self._calculate_weights()

#     def _calculate_weights(self):
#         weights = []
#         for mp in self.mask_paths:
#             try:
#                 m = tifffile.imread(mp)
#                 num_inst = max(0, len(np.unique(m)) - 1)
#                 area = m.shape[0] * m.shape[1]
#                 density = num_inst * (self.patch_size ** 2) / (area + 1e-8)
#                 weights.append(1.0 / (density + self.sparse_threshold))
#             except Exception:
#                 weights.append(1.0)
#         w = torch.DoubleTensor(weights)
#         return w / w.sum() * len(w)

#     @staticmethod
#     def filter_mask_and_ids(mask, max_id=254):
#         mask_filtered = mask.copy()
#         mask_filtered[mask_filtered > max_id] = 0
#         valid_ids = np.unique(mask_filtered)
#         valid_ids = valid_ids[(valid_ids > 0) & (valid_ids <= max_id)]
#         return mask_filtered, valid_ids

#     @staticmethod
#     def get_centroids_and_sizes(mask, valid_ids):
#         centroids = []
#         areas = []
#         spot_masks = []
#         for iid in valid_ids:
#             spot_mask = (mask == iid).astype(np.uint8)
#             props = regionprops(spot_mask)
#             if props:
#                 prop = props[0]
#                 centroids.append(prop.centroid)  # (row, col)
#                 areas.append(prop.area)
#                 spot_masks.append(spot_mask)
#         return centroids, areas, spot_masks

#     @staticmethod
#     def generate_instance_mask(mask_shape, spot_masks):
#         # Union of all spot masks (binary mask for all spots)
#         instance_mask = np.zeros(mask_shape, dtype=np.float32)
#         for spot_mask in spot_masks:
#             instance_mask = np.maximum(instance_mask, spot_mask.astype(np.float32))
#         return instance_mask

#     def _adaptive_heatmap_generation(self, mask, centroids, spot_sizes):
#         h, w = mask.shape
#         y_coords, x_coords = np.meshgrid(np.arange(h), np.arange(w), indexing='ij')
#         combined_heatmap = np.zeros_like(mask, dtype=np.float32)
#         for i, ((cy, cx), size) in enumerate(zip(centroids, spot_sizes)):
#             if size <= 3:
#                 sigma = max(1.0, size * 0.5)
#             elif size <= 8:
#                 sigma = max(2.0, size * 0.4)
#             else:
#                 sigma = max(3.0, size * 0.3)
#             gaussian = np.exp(-((y_coords - cy) ** 2 + (x_coords - cx) ** 2) / (2 * sigma ** 2))
#             combined_heatmap = np.maximum(combined_heatmap, gaussian)
#         return combined_heatmap

#     def _generate_distance_transform(self, mask):
#         semantic = (mask > 0).astype(np.uint8)
#         distance = distance_transform_edt(semantic)
#         max_possible_distance = np.sqrt(self.patch_size ** 2 + self.patch_size ** 2)
#         distance = np.clip(distance / max_possible_distance, 0, 1)
#         return distance.astype(np.float32)

#     def _generate_flow_field(self, mask, centroids):
#         h, w = mask.shape
#         y_coords, x_coords = np.meshgrid(np.arange(h), np.arange(w), indexing='ij')
#         flow_y = np.zeros((h, w), dtype=np.float32)
#         flow_x = np.zeros((h, w), dtype=np.float32)
#         if len(centroids) == 0:
#             return np.stack([flow_y, flow_x], axis=0)
#         centroids_arr = np.array(centroids)
#         spot_mask = (mask > 0)
#         if not spot_mask.any():
#             return np.stack([flow_y, flow_x], axis=0)
#         spot_pixels = np.column_stack(np.where(spot_mask))
#         from scipy.spatial.distance import cdist
#         distances = cdist(spot_pixels, centroids_arr)
#         nearest_centroid_idx = np.argmin(distances, axis=1)
#         for i, (py, px) in enumerate(spot_pixels):
#             nearest_centroid = centroids_arr[nearest_centroid_idx[i]]
#             cy, cx = nearest_centroid
#             dy = cy - py
#             dx = cx - px
#             norm = np.sqrt(dy**2 + dx**2) + 1e-8
#             flow_y[py, px] = dy / norm
#             flow_x[py, px] = dx / norm
#         return np.stack([flow_y, flow_x], axis=0)

#     def __len__(self):
#         return len(self.image_paths)

#     def __getitem__(self, idx):
#         empty_img = torch.zeros((1, self.patch_size, self.patch_size), dtype=torch.float32)
#         empty_mask = torch.zeros((5, self.patch_size, self.patch_size), dtype=torch.float32)
#         empty_flow = torch.zeros((2, self.patch_size, self.patch_size), dtype=torch.float32)
#         empty_conf = torch.zeros((self.patch_size, self.patch_size), dtype=torch.float32)
#         for attempt in range(10):
#             try:
#                 img = tifffile.imread(self.image_paths[idx]).astype(np.float32)
#                 msk = tifffile.imread(self.mask_paths[idx]).astype(np.int32)
#                 img /= (img.max() + 1e-8)
#                 img = np.clip(img, 0, 1)
#                 H, W = img.shape[:2]
#                 x0 = random.randint(0, max(0, H - self.patch_size))
#                 y0 = random.randint(0, max(0, W - self.patch_size))
#                 msk, valid_ids = self.filter_mask_and_ids(msk)
#                 if len(valid_ids) > 0:
#                     iid = random.choice(valid_ids)
#                     coords = np.argwhere(msk == iid)
#                     cy, cx = coords.mean(axis=0).astype(int)
#                     x0 = np.clip(cy - self.patch_size // 2, 0, H - self.patch_size)
#                     y0 = np.clip(cx - self.patch_size // 2, 0, W - self.patch_size)
#                 patch_img = img[x0:x0+self.patch_size, y0:y0+self.patch_size]
#                 patch_msk = msk[x0:x0+self.patch_size, y0:y0+self.patch_size]
#                 ph = self.patch_size - patch_img.shape[0]
#                 pw = self.patch_size - patch_img.shape[1]
#                 if ph > 0 or pw > 0:
#                     patch_img = np.pad(patch_img, ((0, ph), (0, pw)), 'reflect')
#                     patch_msk = np.pad(patch_msk, ((0, ph), (0, pw)), 'constant')
#                 sem = (patch_msk > 0).astype(np.float32)
#                 bnd = np.zeros_like(sem, dtype=np.float32)
#                 for iid in np.unique(patch_msk):
#                     if iid == 0: continue
#                     im = (patch_msk == iid).astype(np.uint8)
#                     kernel = np.ones((2,2), np.uint8)
#                     er = cv2.erode(im, kernel, iterations=1)
#                     bnd += ((im - er) > 0).astype(np.float32)
#                 bnd = np.clip(bnd, 0, 1)
#                 centroids, areas, spot_masks = self.get_centroids_and_sizes(patch_msk, np.unique(patch_msk)[np.unique(patch_msk) > 0])
#                 spot_sizes = [np.sqrt(a/np.pi)*2 for a in areas]  # Diameter from area for Gaussian
#                 instance_mask = self.generate_instance_mask(patch_msk.shape, spot_masks)
#                 gaussian_heatmap = self._adaptive_heatmap_generation(patch_msk, centroids, spot_sizes)
#                 distance_map = self._generate_distance_transform(patch_msk)
#                 flow_field = self._generate_flow_field(patch_msk, centroids)
#                 # Stack all target channels: [semantic, boundary, distance, instance mask, gaussian heatmap]
#                 mask_stack = np.stack([sem, bnd, distance_map, instance_mask, gaussian_heatmap], axis=0)
#                 if self.transform is not None:
#                     img_u8 = (patch_img * 255).astype(np.uint8)
#                     mask_hwc = np.moveaxis(mask_stack, 0, -1)
#                     flow_hwc = np.moveaxis(flow_field, 0, -1)
#                     aug = self.transform(image=img_u8, mask=mask_hwc, flow=flow_hwc)
#                     patch_img = aug['image'].astype(np.float32) / 255.0
#                     mask_stack = np.moveaxis(aug['mask'], -1, 0).astype(np.float32)
#                     flow_field = np.moveaxis(aug.get('flow', flow_hwc), -1, 0).astype(np.float32)
#                     mask_stack[0] = (mask_stack[0] > 0.5).astype(np.float32)
#                     mask_stack[1] = (mask_stack[1] > 0.5).astype(np.float32)
#                 img_t = ToTensorV2()(image=patch_img)['image']
#                 msk_t = torch.from_numpy(mask_stack)
#                 flow_t = torch.from_numpy(flow_field)
#                 semantic = mask_stack[0]
#                 spot_density = (semantic > 0).sum() / (self.patch_size ** 2)
#                 if spot_density < 0.05:
#                     confidence = (semantic > 0).astype(np.float32)
#                 elif spot_density > 0.3:
#                     confidence = np.ones_like(semantic, dtype=np.float32)
#                     confidence[mask_stack[1] > 0] *= 2.0
#                 else:
#                     confidence = np.ones_like(semantic, dtype=np.float32)
#                 confidence_t = torch.from_numpy(confidence)
#                 if len(centroids) == 0:
#                     continue
#                 return img_t, msk_t, flow_t, confidence_t
#             except Exception as e:
#                 print(f"Error in dataset __getitem__ attempt {attempt}: {e}")
#                 continue
#         return empty_img, empty_mask, empty_flow, empty_conf

### FROM CLAUDE 7082025


# import os
# import glob
# import random
# import traceback
# import cv2
# import numpy as np
# import tifffile
# import torch
# import torch.nn as nn
# import torch.nn.functional as F
# from torch.utils.data import Dataset, DataLoader, WeightedRandomSampler
# from sklearn.model_selection import train_test_split
# from skimage.feature import peak_local_max
# from skimage.measure import label, regionprops
# from scipy.spatial.distance import cdist
# from scipy.ndimage import distance_transform_edt
# import albumentations as A
# from albumentations.pytorch import ToTensorV2

# # ====================
# # OPTIMIZED DATASET
# # ====================
# class AdaptiveSpotDataset(Dataset):
#     def __init__(self, image_paths, mask_paths, transform=None, patch_size=128, 
#                  sparse_threshold=0.01):
#         assert len(image_paths) == len(mask_paths)
#         self.image_paths = image_paths
#         self.mask_paths = mask_paths
#         self.transform = transform
#         self.patch_size = patch_size
#         self.sparse_threshold = sparse_threshold
#         self.weights = self._calculate_weights()

#     def _calculate_weights(self):
#         weights = []
#         for mp in self.mask_paths:
#             try:
#                 m = tifffile.imread(mp)
#                 num_inst = max(0, len(np.unique(m)) - 1)
#                 area = m.shape[0] * m.shape[1]
#                 density = num_inst * (self.patch_size ** 2) / (area + 1e-8)
#                 weights.append(1.0 / (density + self.sparse_threshold))
#             except Exception:
#                 weights.append(1.0)
#         w = torch.DoubleTensor(weights)
#         return w / w.sum() * len(w)

#     def _adaptive_heatmap_generation(self, mask, centroids, spot_sizes):
#         """Fixed coordinate system and improved overlap handling"""
#         h, w = mask.shape
        
#         # FIXED: Use consistent meshgrid with indexing='ij' (row, col)
#         y_coords, x_coords = np.meshgrid(np.arange(h), np.arange(w), indexing='ij')
        
#         # Size-adaptive heatmaps
#         fine_heatmap = np.zeros_like(mask, dtype=np.float32)      # Small spots
#         medium_heatmap = np.zeros_like(mask, dtype=np.float32)    # Medium spots  
#         coarse_heatmap = np.zeros_like(mask, dtype=np.float32)    # Large spots
        
#         for (cy, cx), size in zip(centroids, spot_sizes):
#             # FIXED: Improved sigma scaling for better small spot handling
#             sigma = np.clip(size * 0.4, 0.8, 10.0)  # More aggressive scaling
            
#             # FIXED: Consistent coordinate usage - cy=row, cx=col
#             g = np.exp(-((y_coords - cy) ** 2 + (x_coords - cx) ** 2) / (2 * sigma ** 2))
            
#             # FIXED: Additive combination with saturation for better touching spot separation
#             if size <= 3:
#                 fine_heatmap = np.clip(fine_heatmap + g, 0, 1)
#             elif size <= 8:
#                 medium_heatmap = np.clip(medium_heatmap + g, 0, 1)
#             else:
#                 coarse_heatmap = np.clip(coarse_heatmap + g, 0, 1)
        
#         # Don't normalize - keep original intensities for better discrimination
#         return [fine_heatmap, medium_heatmap, coarse_heatmap]

#     def _generate_distance_transform(self, mask):
#         """Fixed distance transform with consistent normalization"""
#         semantic = (mask > 0).astype(np.uint8)
#         distance = distance_transform_edt(semantic)
        
#         # FIXED: Use fixed normalization factor instead of max for consistency
#         # This prevents scale changes between images
#         max_possible_distance = np.sqrt(self.patch_size ** 2 + self.patch_size ** 2)
#         distance = np.clip(distance / max_possible_distance, 0, 1)
        
#         return distance.astype(np.float32)

#     def _generate_flow_field(self, mask, centroids):
#         """Fixed flow field with better handling for touching spots"""
#         h, w = mask.shape
        
#         # FIXED: Consistent coordinate system
#         y_coords, x_coords = np.meshgrid(np.arange(h), np.arange(w), indexing='ij')
        
#         flow_y = np.zeros((h, w), dtype=np.float32)
#         flow_x = np.zeros((h, w), dtype=np.float32)
        
#         if len(centroids) == 0:
#             return np.stack([flow_y, flow_x], axis=0)
        
#         # FIXED: Better flow field generation for touching spots
#         # Use watershed-like approach to assign pixels to nearest centroid
#         centroids_arr = np.array(centroids)  # Shape: (N, 2) where each row is (cy, cx)
        
#         # Create binary mask for all spots
#         spot_mask = (mask > 0)
        
#         if not spot_mask.any():
#             return np.stack([flow_y, flow_x], axis=0)
        
#         # Get all spot pixels
#         spot_pixels = np.column_stack(np.where(spot_mask))  # Shape: (M, 2) where each row is (y, x)
        
#         # Compute distances from each spot pixel to all centroids
#         distances = cdist(spot_pixels, centroids_arr)  # Shape: (M, N)
#         nearest_centroid_idx = np.argmin(distances, axis=1)
        
#         # Generate flow vectors only for spot pixels
#         for i, (py, px) in enumerate(spot_pixels):
#             nearest_centroid = centroids_arr[nearest_centroid_idx[i]]
#             cy, cx = nearest_centroid
            
#             # Normalized flow vectors for stability
#             dy = cy - py
#             dx = cx - px
#             norm = np.sqrt(dy**2 + dx**2) + 1e-8
            
#             flow_y[py, px] = dy / norm
#             flow_x[py, px] = dx / norm
        
#         return np.stack([flow_y, flow_x], axis=0)

#     def __len__(self):
#         return len(self.image_paths)

#     def __getitem__(self, idx):
#         empty_img = torch.zeros((1, self.patch_size, self.patch_size), dtype=torch.float32)
#         empty_mask = torch.zeros((6, self.patch_size, self.patch_size), dtype=torch.float32)
#         empty_flow = torch.zeros((2, self.patch_size, self.patch_size), dtype=torch.float32)
#         empty_conf = torch.zeros((self.patch_size, self.patch_size), dtype=torch.float32)

#         for attempt in range(10):
#             try:
#                 img = tifffile.imread(self.image_paths[idx]).astype(np.float32)
#                 msk = tifffile.imread(self.mask_paths[idx]).astype(np.int32)
#                 img /= (img.max() + 1e-8)
#                 img = np.clip(img, 0, 1)

#                 H, W = img.shape[:2]
#                 x0 = random.randint(0, max(0, H - self.patch_size))
#                 y0 = random.randint(0, max(0, W - self.patch_size))
                
#                 # Bias towards areas with spots
#                 ids = np.unique(msk)
#                 ids = ids[ids > 0]
#                 if len(ids) > 0:
#                     iid = random.choice(ids)
#                     coords = np.argwhere(msk == iid)
#                     cy, cx = coords.mean(axis=0).astype(int)
#                     x0 = np.clip(cy - self.patch_size // 2, 0, H - self.patch_size)
#                     y0 = np.clip(cx - self.patch_size // 2, 0, W - self.patch_size)

#                 patch_img = img[x0:x0+self.patch_size, y0:y0+self.patch_size]
#                 patch_msk = msk[x0:x0+self.patch_size, y0:y0+self.patch_size]
                
#                 # Padding if needed
#                 ph = self.patch_size - patch_img.shape[0]
#                 pw = self.patch_size - patch_img.shape[1]
#                 if ph > 0 or pw > 0:
#                     patch_img = np.pad(patch_img, ((0, ph), (0, pw)), 'reflect')
#                     patch_msk = np.pad(patch_msk, ((0, ph), (0, pw)), 'constant')

#                 # Generate semantic mask
#                 sem = (patch_msk > 0).astype(np.float32)
                
#                 # Generate boundary mask with better precision for touching spots
#                 bnd = np.zeros_like(sem, dtype=np.float32)
#                 for iid in np.unique(patch_msk):
#                     if iid == 0: continue
#                     im = (patch_msk == iid).astype(np.uint8)
#                     # Use smaller kernel for finer boundary detection
#                     kernel = np.ones((2,2), np.uint8)
#                     er = cv2.erode(im, kernel, iterations=1)
#                     bnd += ((im - er) > 0).astype(np.float32)
#                 bnd = np.clip(bnd, 0, 1)

#                 # Analyze spot properties
#                 centroids = []
#                 spot_sizes = []
#                 for iid in np.unique(patch_msk):
#                     if iid == 0: 
#                         continue
                        
#                     inst = (patch_msk == iid).astype(np.uint8)
#                     props = regionprops(inst)
                    
#                     if len(props) > 0:
#                         prop = props[0]
#                         cy, cx = prop.centroid  # Returns (row, col)
#                         centroids.append([cy, cx])
#                         spot_sizes.append(prop.equivalent_diameter)

#                 # Generate adaptive heatmaps
#                 heatmaps = self._adaptive_heatmap_generation(patch_msk, centroids, spot_sizes)
                
#                 # Generate distance transform
#                 distance_map = self._generate_distance_transform(patch_msk)
                
#                 # Generate flow field
#                 flow_field = self._generate_flow_field(patch_msk, centroids)

#                 # Stack all target channels: [semantic, boundary, distance, heatmap1, heatmap2, heatmap3]
#                 mask_stack = np.stack([sem, bnd, distance_map] + heatmaps, axis=0)

#                 # Apply augmentation
#                 if self.transform is not None:
#                     img_u8 = (patch_img * 255).astype(np.uint8)
#                     mask_hwc = np.moveaxis(mask_stack, 0, -1)
#                     flow_hwc = np.moveaxis(flow_field, 0, -1)
                    
#                     # Combined augmentation
#                     aug = self.transform(image=img_u8, mask=mask_hwc, flow=flow_hwc)
#                     patch_img = aug['image'].astype(np.float32) / 255.0
#                     mask_stack = np.moveaxis(aug['mask'], -1, 0).astype(np.float32)
#                     flow_field = np.moveaxis(aug.get('flow', flow_hwc), -1, 0).astype(np.float32)
                    
#                     # Binarize semantic and boundary
#                     mask_stack[0] = (mask_stack[0] > 0.5).astype(np.float32)
#                     mask_stack[1] = (mask_stack[1] > 0.5).astype(np.float32)

#                 # Convert to tensors
#                 img_t = ToTensorV2()(image=patch_img)['image']
#                 msk_t = torch.from_numpy(mask_stack)
#                 flow_t = torch.from_numpy(flow_field)

#                 # Enhanced confidence mask for dense regions
#                 semantic = mask_stack[0]
#                 spot_density = (semantic > 0).sum() / (self.patch_size ** 2)
                
#                 if spot_density < 0.05:  # Sparse regions
#                     confidence = (semantic > 0).astype(np.float32)
#                 elif spot_density > 0.3:  # Dense regions - weight boundaries more
#                     confidence = np.ones_like(semantic, dtype=np.float32)
#                     confidence[mask_stack[1] > 0] *= 2.0  # Double weight for boundaries
#                 else:
#                     confidence = np.ones_like(semantic, dtype=np.float32)
                
#                 confidence_t = torch.from_numpy(confidence)

#                 # Skip if no spots
#                 if len(centroids) == 0:
#                     continue
                
#                 return img_t, msk_t, flow_t, confidence_t
                        
#             except Exception as e:
#                 print(f"Error in dataset __getitem__ attempt {attempt}: {e}")
#                 continue

#         return empty_img, empty_mask, empty_flow, empty_conf

##FROM CHAT 7/11/2025
import torch
import torch.nn as nn
import torch.nn.functional as F

# -------------------------------------------------------------------
# 1) SE‑Block (unchanged)
# -------------------------------------------------------------------
class SEBlock(nn.Module):
    def __init__(self, channels, reduction=16):
        super().__init__()
        self.avg = nn.AdaptiveAvgPool2d(1)
        self.fc  = nn.Sequential(
            nn.Linear(channels, channels//reduction, bias=False),
            nn.ReLU(inplace=True),
            nn.Linear(channels//reduction, channels, bias=False),
            nn.Sigmoid()
        )
    def forward(self, x):
        b,c,_,_ = x.shape
        y = self.avg(x).view(b,c)
        y = self.fc(y).view(b,c,1,1)
        return x * y

# -------------------------------------------------------------------
# 2) Depthwise‑Separable ASPP
# -------------------------------------------------------------------
class SepASPP(nn.Module):
    def __init__(self, in_ch, out_ch, rates=[1,4,8]):
        super().__init__()
        self.convs = nn.ModuleList([
            nn.Sequential(
                nn.Conv2d(in_ch, in_ch, kernel_size=3, padding=r, dilation=r, groups=in_ch, bias=False),
                nn.BatchNorm2d(in_ch),
                nn.ReLU(inplace=True),
                nn.Conv2d(in_ch, out_ch, kernel_size=1, bias=False),
                nn.BatchNorm2d(out_ch),
                nn.ReLU(inplace=True),
            )
            for r in rates
        ])
        self.project = nn.Conv2d(len(rates)*out_ch, out_ch, kernel_size=1, bias=False)
        self.dropout = nn.Dropout2d(0.1)

    def forward(self, x):
        feats = [conv(x) for conv in self.convs]
        x = self.project(torch.cat(feats, dim=1))
        return self.dropout(x)

# -------------------------------------------------------------------
# 3) Main Detector
# -------------------------------------------------------------------
class AdaptiveSpotDetector(nn.Module):
    def __init__(self,
                 in_ch=1,
                 base_ch=32,
                 num_heatmaps=2,      # [soft‑instance, centroid]
                 max_offset=16,
                 dropout_p=0.2):
        super().__init__()
        # Stem + encoder
        self.stem = nn.Sequential(
            nn.Conv2d(in_ch, base_ch, 3, stride=2, padding=1, bias=False),
            nn.BatchNorm2d(base_ch),
            nn.ReLU(inplace=True),
        )
        self.enc1 = self._enc_block(base_ch,  base_ch*2)
        self.enc2 = self._enc_block(base_ch*2, base_ch*4)
        self.enc3 = self._enc_block(base_ch*4, base_ch*8)
        self.enc4 = self._enc_block(base_ch*8, base_ch*16)

        # Bottleneck + SE + ASPP
        self.bottleneck = nn.Sequential(
            nn.Conv2d(base_ch*16, base_ch*16, 3, padding=1, bias=False),
            nn.BatchNorm2d(base_ch*16),
            nn.ReLU(inplace=True),
            nn.Dropout2d(dropout_p),
            nn.Conv2d(base_ch*16, base_ch*16, 3, padding=1, bias=False),
            nn.BatchNorm2d(base_ch*16),
            nn.ReLU(inplace=True),
            nn.Dropout2d(dropout_p),
        )
        self.se   = SEBlock(base_ch*16)
        self.aspp = SepASPP(base_ch*16, base_ch*16, rates=[1,4,8])

        # Decoder: Upsample + Conv + Dropout
        def up_block(in_c, out_c):
            return nn.Sequential(
                nn.Upsample(scale_factor=2, mode='bilinear', align_corners=False),
                nn.Conv2d(in_c, out_c, 3, padding=1, bias=False),
                nn.BatchNorm2d(out_c),
                nn.ReLU(inplace=True),
                nn.Dropout2d(dropout_p),
            )

        self.dec4 = up_block(base_ch*16, base_ch*8)
        self.dec3 = up_block(base_ch*8,  base_ch*4)
        self.dec2 = up_block(base_ch*4,  base_ch*2)
        self.dec1 = up_block(base_ch*2,  base_ch)

        # 1×1 to match skip dims
        self.skip4 = nn.Conv2d(base_ch*8, base_ch*8, 1, bias=False)
        self.skip3 = nn.Conv2d(base_ch*4, base_ch*4, 1, bias=False)
        self.skip2 = nn.Conv2d(base_ch*2, base_ch*2, 1, bias=False)
        self.skip1 = nn.Conv2d(base_ch,   base_ch,   1, bias=False)

        # Fusion 1×1 after skip-add
        self.f4 = nn.Conv2d(base_ch*16, base_ch*8, 1, bias=False)
        self.f3 = nn.Conv2d(base_ch*8,  base_ch*4, 1, bias=False)
        self.f2 = nn.Conv2d(base_ch*4,  base_ch*2, 1, bias=False)
        self.f1 = nn.Conv2d(base_ch*2,  base_ch,   1, bias=False)

        # Deep supervision heads on centroid heatmaps
        self.ds4_hm = nn.Conv2d(base_ch*8, num_heatmaps, 1)
        self.ds3_hm = nn.Conv2d(base_ch*4, num_heatmaps, 1)
        self.ds2_hm = nn.Conv2d(base_ch*2, num_heatmaps, 1)

        # Deep supervision heads on flow
        self.ds4_flow = nn.Conv2d(base_ch*8, 2, 1)
        self.ds3_flow = nn.Conv2d(base_ch*4, 2, 1)
        self.ds2_flow = nn.Conv2d(base_ch*2, 2, 1)

        # Final heads
        self.semantic_head = nn.Conv2d(base_ch, 1, 1)
        self.boundary_head = nn.Conv2d(base_ch, 1, 1)
        self.distance_head = nn.Conv2d(base_ch, 1, 1)
        self.heatmap_head = nn.Conv2d(base_ch, num_heatmaps, 1)
        self.flow_head    = nn.Sequential(
            nn.Conv2d(base_ch, base_ch//2, 3, padding=1, bias=False),
            nn.BatchNorm2d(base_ch//2),
            nn.ReLU(inplace=True),
            nn.Conv2d(base_ch//2, 2, 1),
            nn.Tanh()
        )
        self.max_offset = max_offset

    def _enc_block(self, in_c, out_c):
        return nn.Sequential(
            nn.Conv2d(in_c, out_c, 3, padding=1, bias=False),
            nn.BatchNorm2d(out_c),
            nn.ReLU(inplace=True),
            nn.Conv2d(out_c, out_c, 3, padding=1, bias=False),
            nn.BatchNorm2d(out_c),
            nn.ReLU(inplace=True),
            nn.MaxPool2d(2,2)
        )

    def forward(self, x):
        # Encoder
        x0 = self.stem(x)
        x1 = self.enc1(x0)
        x2 = self.enc2(x1)
        x3 = self.enc3(x2)
        x4 = self.enc4(x3)

        # Bottleneck
        xb = self.bottleneck(x4)
        xb = self.se(xb)
        xb = self.aspp(xb)

        # Decoder + skip + fuse
        d4 = self.dec4(xb)
        d4 = F.interpolate(d4, size=x4.shape[2:], mode='bilinear', align_corners=False)
        d4 = self.skip4(d4) + self.f4(x4)

        d3 = self.dec3(d4)
        d3 = F.interpolate(d3, size=x3.shape[2:], mode='bilinear', align_corners=False)
        d3 = self.skip3(d3) + self.f3(x3)

        d2 = self.dec2(d3)
        d2 = F.interpolate(d2, size=x2.shape[2:], mode='bilinear', align_corners=False)
        d2 = self.skip2(d2) + self.f2(x2)

        d1 = self.dec1(d2)
        d1 = F.interpolate(d1, size=x1.shape[2:], mode='bilinear', align_corners=False)
        d1 = self.skip1(d1) + self.f1(x1)

        # final full‑res feature
        feat = F.interpolate(d1, size=x.shape[2:], mode='bilinear', align_corners=False)

        # deep supervision maps (upsampled to full res)
        ds4_h = F.interpolate(self.ds4_hm(d4), size=x.shape[2:], mode='bilinear', align_corners=False)
        ds3_h = F.interpolate(self.ds3_hm(d3), size=x.shape[2:], mode='bilinear', align_corners=False)
        ds2_h = F.interpolate(self.ds2_hm(d2), size=x.shape[2:], mode='bilinear', align_corners=False)

        ds4_f = F.interpolate(self.ds4_flow(d4), size=x.shape[2:], mode='bilinear', align_corners=False)
        ds3_f = F.interpolate(self.ds3_flow(d3), size=x.shape[2:], mode='bilinear', align_corners=False)
        ds2_f = F.interpolate(self.ds2_flow(d2), size=x.shape[2:], mode='bilinear', align_corners=False)

        # final heads
        sem_out   = self.semantic_head(feat)
        bnd_out   = self.boundary_head(feat)
        dist_out  = self.distance_head(feat)
        hm_out    = self.heatmap_head(feat)       # (B,2,H,W)
        flow_out  = self.flow_head(feat) * self.max_offset  # (B,2,H,W)

        return {
            'semantic': sem_out,
            'boundary': bnd_out,
            'distance': dist_out,
            'heatmaps': hm_out,
            'flow': flow_out,
            'deep_hm':  [ds4_h, ds3_h, ds2_h],
            'deep_flow': [ds4_f, ds3_f, ds2_f],
        }

# -------------------------------------------------------------------
# 4) Charbonnier helper
# -------------------------------------------------------------------
def charbonnier(pred, target, eps=1e-3):
    return torch.mean(torch.sqrt((pred - target)**2 + eps**2))

# -------------------------------------------------------------------
# 5) AdaptiveSpotLoss w/ normalization, dice/focal, charb., deep‑flow
# -------------------------------------------------------------------
class AdaptiveSpotLoss(nn.Module):
    def __init__(self, epsilon=1e-6):
        super().__init__()
        self.eps = epsilon
        # weights
        self.w_sem    = 1.0
        self.w_bnd    = 2.0
        self.w_dist   = 1.0
        self.w_inst   = 2.0
        self.w_hm     = 3.0
        self.w_flow   = 1.0
        self.w_ds     = 0.5
        self.w_flowds = 0.5

    def _focal(self, logits, target, alpha=0.25, gamma=2.0):
        ce = F.binary_cross_entropy_with_logits(logits, target, reduction='none')
        pt = torch.exp(-ce)
        return (alpha * (1-pt)**gamma * ce).mean()

    def _dice(self, logits, target, conf=None, smooth=1e-6):
        prob = torch.sigmoid(logits)
        inter = (prob * target)
        union = prob + target
        if conf is not None:
            inter *= conf; union *= conf
        inter = inter.sum(dim=[2,3])
        union = union.sum(dim=[2,3])
        dice = (2*inter + smooth)/(union + smooth)
        return 1 - dice.mean()

    def forward(self, outputs, targets, flow_targets, confidence=None):
        losses = {}

        # unpack targets
        gt_sem     = targets[:,0:1]
        gt_bnd     = targets[:,1:2]
        gt_dist    = targets[:,2:3]
        gt_inst    = targets[:,3:4]
        gt_cent    = targets[:,4:5]

        # normalize centroid maps to unit L2 sum
        sumc = gt_cent.sum(dim=[2,3], keepdim=True).clamp(min=self.eps, max=100.0)
        gt_cent_n = gt_cent / sumc

        # semantic
        l1 = self._focal(outputs['semantic'], gt_sem)
        l2 = self._dice(outputs['semantic'], gt_sem, confidence)
        losses['semantic'] = l1 + l2

        # boundary
        l1 = self._focal(outputs['boundary'], gt_bnd)
        l2 = self._dice(outputs['boundary'], gt_bnd, confidence)
        losses['boundary'] = l1 + l2

        # distance
        losses['distance'] = F.mse_loss(torch.sigmoid(outputs['distance']), gt_dist)

        # instance mask
        l1 = self._focal(outputs['heatmaps'][:,0:1], gt_inst)
        l2 = self._dice(outputs['heatmaps'][:,0:1], gt_inst, confidence)
        losses['instance'] = l1 + l2

        # centroid heatmap
        l1 = self._focal(outputs['heatmaps'][:,1:2], gt_cent_n)
        l2 = self._dice(outputs['heatmaps'][:,1:2], gt_cent_n, confidence)
        l3 = F.mse_loss(torch.sigmoid(outputs['heatmaps'][:,1:2]), gt_cent_n)
        losses['centroid'] = l1 + l2 + l3

        # flow (Charbonnier) only inside object mask
        mask = (gt_sem>0).float()
        losses['flow'] = charbonnier(outputs['flow']*mask, flow_targets*mask)

        # deep supervision heatmap
        ds_h = 0.0
        for dsh in outputs['deep_hm']:
            ds_h += self._focal(dsh[:,1:2], gt_cent_n)
        losses['deep_hm'] = ds_h/len(outputs['deep_hm'])

        # deep supervision flow
        ds_f = 0.0
        for dsf in outputs['deep_flow']:
            ds_f += charbonnier(dsf*mask, flow_targets*mask)
        losses['deep_flow'] = ds_f/len(outputs['deep_flow'])

        # total
        total = (
            self.w_sem   * losses['semantic'] +
            self.w_bnd   * losses['boundary'] +
            self.w_dist  * losses['distance'] +
            self.w_inst  * losses['instance'] +
            self.w_hm    * losses['centroid'] +
            self.w_flow  * losses['flow'] +
            self.w_ds    * losses['deep_hm'] +
            self.w_flowds* losses['deep_flow']
        )
        losses['total'] = total

        # return scalar loss + dict of components
        return total, {k: float(v) for k,v in losses.items()}


### FROM CLAUDE 7082025
# # ====================
# # OPTIMIZED MODEL
# # ====================
# class SEBlock(nn.Module):
#     def __init__(self, channels, reduction=16):
#         super().__init__()
#         self.avg = nn.AdaptiveAvgPool2d(1)
#         self.fc = nn.Sequential(
#             nn.Linear(channels, channels//reduction, bias=False),
#             nn.ReLU(inplace=True),
#             nn.Linear(channels//reduction, channels, bias=False),
#             nn.Sigmoid()
#         )
#     def forward(self, x):
#         b,c,_,_ = x.shape
#         y = self.avg(x).view(b,c)
#         y = self.fc(y).view(b,c,1,1)
#         return x * y

# class MultiScaleASPP(nn.Module):
#     def __init__(self, in_ch, out_ch):
#         super().__init__()
#         self.a1 = nn.Conv2d(in_ch, out_ch, 1)
#         self.a2 = nn.Conv2d(in_ch, out_ch, 3, padding=2, dilation=2)
#         self.a3 = nn.Conv2d(in_ch, out_ch, 3, padding=4, dilation=4)
#         self.a4 = nn.Conv2d(in_ch, out_ch, 3, padding=6, dilation=6)
#         self.a5 = nn.Conv2d(in_ch, out_ch, 3, padding=8, dilation=8)
#         self.out = nn.Conv2d(out_ch*5, out_ch, 1)
#         self.dropout = nn.Dropout2d(0.1)
    
#     def forward(self, x):
#         x1 = self.a1(x)
#         x2 = self.a2(x)
#         x3 = self.a3(x)
#         x4 = self.a4(x)
#         x5 = self.a5(x)
#         out = self.out(torch.cat([x1,x2,x3,x4,x5], dim=1))
#         return self.dropout(out)

# #In AdaptiveSpotDetector, change num_heatmaps=2 (for soft mask + centroid)
# class AdaptiveSpotDetector(nn.Module):
#     def __init__(self, in_ch=1, num_heatmaps=2, base_ch=32, max_offset=16, dropout_p=0.2):
#         super().__init__()
#         # ENCODER with residual connections
#         self.stem = nn.Sequential(
#             nn.Conv2d(in_ch, base_ch, 3, stride=2, padding=1),
#             nn.BatchNorm2d(base_ch),
#             nn.ReLU(inplace=True)
#         )
#         self.enc1 = self._enc_block(base_ch, base_ch*2)
#         self.enc2 = self._enc_block(base_ch*2, base_ch*4)
#         self.enc3 = self._enc_block(base_ch*4, base_ch*8)
#         self.enc4 = self._enc_block(base_ch*8, base_ch*16)

#         # BOTTLENECK with attention and dropout
#         self.bottleneck = nn.Sequential(
#             nn.Conv2d(base_ch*16, base_ch*16, 3, padding=1),
#             nn.BatchNorm2d(base_ch*16),
#             nn.ReLU(inplace=True),
#             nn.Dropout2d(dropout_p),
#             nn.Conv2d(base_ch*16, base_ch*16, 3, padding=1),
#             nn.BatchNorm2d(base_ch*16),
#             nn.ReLU(inplace=True),
#             nn.Dropout2d(dropout_p),
#         )
#         self.se = SEBlock(base_ch*16)
#         self.aspp = MultiScaleASPP(base_ch*16, base_ch*16)

#         # DECODER with skip connections and dropout
#         self.dec4 = nn.Sequential(
#             nn.ConvTranspose2d(base_ch*16, base_ch*8, 2, stride=2),
#             nn.Dropout2d(dropout_p)
#         )
#         self.dec3 = nn.Sequential(
#             nn.ConvTranspose2d(base_ch*8, base_ch*4, 2, stride=2),
#             nn.Dropout2d(dropout_p)
#         )
#         self.dec2 = nn.Sequential(
#             nn.ConvTranspose2d(base_ch*4, base_ch*2, 2, stride=2),
#             nn.Dropout2d(dropout_p)
#         )
#         self.dec1 = nn.Sequential(
#             nn.ConvTranspose2d(base_ch*2, base_ch, 2, stride=2),
#             nn.Dropout2d(dropout_p)
#         )

#         # Skip connection matching
#         self.skip4 = nn.Conv2d(base_ch*8, base_ch*8, 1)
#         self.skip3 = nn.Conv2d(base_ch*4, base_ch*4, 1)
#         self.skip2 = nn.Conv2d(base_ch*2, base_ch*2, 1)
#         self.skip1 = nn.Conv2d(base_ch, base_ch, 1)

#         # Feature fusion
#         self.fusion4 = nn.Conv2d(base_ch*16, base_ch*8, 1)
#         self.fusion3 = nn.Conv2d(base_ch*8, base_ch*4, 1)
#         self.fusion2 = nn.Conv2d(base_ch*4, base_ch*2, 1)
#         self.fusion1 = nn.Conv2d(base_ch*2, base_ch, 1)

#         # Deep supervision
#         self.ds4 = nn.Conv2d(base_ch*8, num_heatmaps, 1)
#         self.ds3 = nn.Conv2d(base_ch*4, num_heatmaps, 1)
#         self.ds2 = nn.Conv2d(base_ch*2, num_heatmaps, 1)

#         # Output heads
#         self.semantic_head = nn.Conv2d(base_ch, 1, 1)
#         self.boundary_head = nn.Conv2d(base_ch, 1, 1)
#         self.distance_head = nn.Conv2d(base_ch, 1, 1)
#         self.heatmap_head = nn.Conv2d(base_ch, num_heatmaps, 1)
#         self.flow_head = nn.Sequential(
#             nn.Conv2d(base_ch, base_ch//2, 3, padding=1),
#             nn.ReLU(inplace=True),
#             nn.Conv2d(base_ch//2, 2, 1),
#             nn.Tanh()
#         )
#         self.max_offset = max_offset

#     def _enc_block(self, in_c, out_c):
#         return nn.Sequential(
#             nn.Conv2d(in_c, out_c, 3, padding=1),
#             nn.BatchNorm2d(out_c),
#             nn.ReLU(inplace=True),
#             nn.Conv2d(out_c, out_c, 3, padding=1),
#             nn.BatchNorm2d(out_c),
#             nn.ReLU(inplace=True),
#             nn.MaxPool2d(2, 2)
#         )

#     def forward(self, x):
#         # Encoder
#         x0 = self.stem(x)
#         x1 = self.enc1(x0)
#         x2 = self.enc2(x1)
#         x3 = self.enc3(x2)
#         x4 = self.enc4(x3)

#         # Bottleneck
#         x5 = self.bottleneck(x4)
#         x5 = self.se(x5)
#         x5 = self.aspp(x5)

#         # Decoder with skip connections
#         d4 = self.dec4(x5)
#         d4 = F.interpolate(d4, size=x4.shape[2:], mode='bilinear', align_corners=False)
#         d4 = self.skip4(d4) + self.fusion4(x4)

#         d3 = self.dec3(d4)
#         d3 = F.interpolate(d3, size=x3.shape[2:], mode='bilinear', align_corners=False)
#         d3 = self.skip3(d3) + self.fusion3(x3)

#         d2 = self.dec2(d3)
#         d2 = F.interpolate(d2, size=x2.shape[2:], mode='bilinear', align_corners=False)
#         d2 = self.skip2(d2) + self.fusion2(x2)

#         d1 = self.dec1(d2)
#         d1 = F.interpolate(d1, size=x1.shape[2:], mode='bilinear', align_corners=False)
#         d1 = self.skip1(d1) + self.fusion1(x1)

#         # Final upsampling
#         d1 = F.interpolate(d1, size=x.shape[2:], mode='bilinear', align_corners=False)

#         # Deep supervision outputs
#         ds4_out = F.interpolate(self.ds4(d4), size=x.shape[2:], mode='bilinear', align_corners=False)
#         ds3_out = F.interpolate(self.ds3(d3), size=x.shape[2:], mode='bilinear', align_corners=False)
#         ds2_out = F.interpolate(self.ds2(d2), size=x.shape[2:], mode='bilinear', align_corners=False)

#         # Output heads
#         semantic_out = self.semantic_head(d1)
#         boundary_out = self.boundary_head(d1)
#         distance_out = self.distance_head(d1)
#         heatmap_out = self.heatmap_head(d1)  # shape: (B, 2, H, W)
#         flow_out = self.flow_head(d1) * self.max_offset

#         return {
#             'semantic': semantic_out,
#             'boundary': boundary_out,
#             'distance': distance_out,
#             'heatmaps': heatmap_out, # shape: (B, 2, H, W)
#             'flow': flow_out,
#             'deep_supervision': [ds4_out, ds3_out, ds2_out]
#         }

# # ====================
# # OPTIMIZED LOSS
# # ====================
# # ====================
# # OPTIMIZED LOSS (for new loader)
# # ====================
# class AdaptiveSpotLoss(nn.Module):
#     def __init__(self, epsilon=1e-6):
#         super().__init__()
#         self.eps = epsilon
#         self.focal_loss = self._focal_loss

#         # Loss weights
#         self.w_semantic = 2.0
#         self.w_boundary = 1.0
#         self.w_distance =0.5
#         self.w_instance = 1.0      # For instance mask (segmentation)
#         self.w_heatmap = 3.0       # For Gaussian heatmap (detection)
#         self.w_flow = 1.0
#         self.w_ds = 0.5

#     def _focal_loss(self, pred, target, alpha=0.25, gamma=2.5):
#         ce_loss = F.binary_cross_entropy_with_logits(pred, target, reduction='none')
#         pt = torch.exp(-ce_loss)
#         focal_loss = alpha * (1-pt)**gamma * ce_loss
#         return focal_loss.mean()

#     def _dice_loss(self, pred, target, confidence=None, smooth=1e-6):
#         pred = torch.sigmoid(pred)
#         intersection = (pred * target)
#         union = pred + target
#         if confidence is not None:
#             intersection = intersection * confidence
#             union = union * confidence
#         intersection = intersection.sum(dim=[2,3])
#         union = union.sum(dim=[2,3])
#         dice = (2 * intersection + smooth) / (union + smooth)
#         return 1 - dice.mean()

#     def _tversky_loss(self, pred, target, alpha=0.7, beta=0.3, smooth=1e-6):
#         pred = torch.sigmoid(pred)
#         tp = (pred * target).sum(dim=[2,3])
#         fn = ((1 - pred) * target).sum(dim=[2,3])
#         fp = (pred * (1 - target)).sum(dim=[2,3])
#         tversky = (tp + smooth) / (tp + alpha * fn + beta * fp + smooth)
#         return 1 - tversky.mean()

#     def forward(self, outputs, targets, flow_targets, confidence=None):
#         losses = {}

#         # Unpack targets: [semantic, boundary, distance, instance_mask, gaussian_heatmap]
#         gt_semantic = targets[:, 0:1]
#         gt_boundary = targets[:, 1:2]
#         gt_distance = targets[:, 2:3]
#         gt_soft_instance = targets[:, 3:4]  # soft-blurred instance mask
#         gt_centroid = targets[:, 4:5]       # centroid map     # gaussian heatmap (detection)

#         # Semantic segmentation loss
#         sem_focal = self._focal_loss(outputs['semantic'], gt_semantic)
#         sem_dice = self._tversky_loss(outputs['semantic'], gt_semantic, confidence)
#         losses['semantic'] = sem_focal + sem_dice

#         # Boundary detection loss
#         bnd_focal = self._focal_loss(outputs['boundary'], gt_boundary)
#         bnd_dice = self._dice_loss(outputs['boundary'], gt_boundary, confidence)
#         losses['boundary'] = bnd_focal + bnd_dice

#         # Distance transform loss
#         dist_mse = F.mse_loss(torch.sigmoid(outputs['distance']), gt_distance)
#         losses['distance'] = dist_mse

#         # Soft-blurred instance mask (segmentation) loss
#         instance_focal = self._focal_loss(outputs['heatmaps'][:, 0:1], gt_soft_instance)
#         instance_dice = self._tversky_loss(outputs['heatmaps'][:, 0:1], gt_soft_instance, confidence)
#         losses['instance'] = instance_focal + instance_dice

#         # Centroid map (detection) loss
#         centroid_focal = self._focal_loss(outputs['heatmaps'][:, 1:2], gt_centroid)
#         centroid_dice = self._dice_loss(outputs['heatmaps'][:, 1:2], gt_centroid, confidence)
#         centroid_mse = F.mse_loss(torch.sigmoid(outputs['heatmaps'][:, 1:2]), gt_centroid)
#         losses['centroid'] = centroid_focal + centroid_dice + centroid_mse

#         # Flow field loss (only where spots exist)
#         flow_mask = (gt_semantic > 0).float()
#         flow_loss = F.mse_loss(outputs['flow'] * flow_mask, flow_targets * flow_mask)
#         losses['flow'] = flow_loss

#         # Deep supervision loss (optional, if you use it)
#         ds_loss = 0.0
#         for ds_pred in outputs['deep_supervision']:
#             ds_loss += self._focal_loss(ds_pred[:, 0:1], gt_centroid)
#         losses['deep_supervision'] = ds_loss / max(1, len(outputs['deep_supervision']))

#         # Total loss (update weights as needed)
#         total_loss = (
#             self.w_semantic * losses['semantic'] +
#             self.w_boundary * losses['boundary'] +
#             self.w_distance * losses['distance'] +
#             self.w_instance * losses['instance'] +
#             self.w_heatmap * losses['centroid'] +
#             self.w_flow * losses['flow'] +
#             self.w_ds * losses['deep_supervision']
#         )
#         losses['total'] = total_loss
#         loss_items = {k: v.item() if torch.is_tensor(v) else v for k, v in losses.items()}
#         return total_loss, loss_items

## FROM CHAT /07112025
import numpy as np
from skimage.feature import peak_local_max

def extract_precise_spots(centroid_map,
                          semantic_mask,
                          flow=None,
                          min_distance=2,
                          threshold=0.3,
                          flow_iters=5,
                          flow_step=1.0):
    """
    Extract and refine spots from centroid + flow maps.

    Args:
        centroid_map (H×W): predicted heatmap (sigmoid output).
        semantic_mask (H×W): binary or float mask (e.g., sigmoid > 0.5).
        flow (2×H×W): flow vectors [dy, dx], normalized and scaled.
        min_distance (int): minimum distance between peaks.
        threshold (float): threshold for centroid score.
        flow_iters (int): number of refinement steps along flow.
        flow_step (float): step size for refinement.

    Returns:
        List of [x, y, score] for each predicted spot.
    """
    # 1. Restrict search to valid semantic area
    mask = (semantic_mask > 0.5).astype(np.float32)
    response = centroid_map * mask

    # 2. Detect peaks above threshold
    spots = []
    if response.max() > threshold:
        coords = peak_local_max(response,
                                min_distance=min_distance,
                                threshold_abs=threshold,
                                exclude_border=False)
        for y, x in coords:
            score = float(response[y, x])
            spots.append([x, y, score])

    spots = np.array(spots, dtype=np.float32)

    # 3. Optional flow-based refinement
    if flow is not None and len(spots) > 0:
        spots = refine_with_flow(spots, flow, n_iters=flow_iters, step_size=flow_step)

    return spots.tolist()
  
def refine_with_flow(spots, flow, n_iters=5, step_size=1.0):
    """
    Refine spot positions by iteratively following the flow vectors.
    Args:
        spots: N×3 array of [x, y, score]
        flow:  2×H×W array [dy, dx]
    Returns:
        Refined N×3 array
    """
    H, W = flow.shape[1:]
    refined = spots.copy()

    for i in range(n_iters):
        for idx, (x, y, score) in enumerate(refined):
            ix, iy = int(round(x)), int(round(y))
            if 0 <= iy < H and 0 <= ix < W:
                dy, dx = flow[:, iy, ix]
                x += dx * step_size
                y += dy * step_size
                x = np.clip(x, 0, W - 1)
                y = np.clip(y, 0, H - 1)
                refined[idx, 0] = x
                refined[idx, 1] = y

    return refined

from torch.utils.data import DataLoader, WeightedRandomSampler
from sklearn.model_selection import train_test_split
import albumentations as A
from albumentations.pytorch import ToTensorV2

# ====================
# TRANSFORMS
# ====================
def get_transforms(patch_size=128):
    """Augmentations for training (Albumentations with flow support)"""
    return A.Compose([
        A.RandomRotate90(p=0.5),
        A.HorizontalFlip(p=0.5),
        A.VerticalFlip(p=0.5),
        A.RandomBrightnessContrast(brightness_limit=0.2, contrast_limit=0.2, p=0.4),
        A.GaussianBlur(blur_limit=(1, 2), sigma_limit=0.3, p=0.3),
        # A.OneOf([
        #     A.ElasticTransform(alpha=1, sigma=30, alpha_affine=30, p=0.5),
        #     A.GridDistortion(num_steps=5, distort_limit=0.3, p=0.5),
        #     A.OpticalDistortion(distort_limit=0.5, shift_limit=0.5, p=0.5),
        # ], p=0.3),
    ], additional_targets={'flow': 'mask'})  # Treat flow as dense mask

# ====================
# DATA LOADER SETUP
# ====================
def create_data_loaders(image_paths, mask_paths, batch_size=8, patch_size=128, num_workers=4):
    """
    Create train/val DataLoaders using AdaptiveSpotDataset and weighted sampling.
    """
    # Split data
    train_imgs, val_imgs, train_masks, val_masks = train_test_split(
        image_paths, mask_paths, test_size=0.2, random_state=42
    )

    # Datasets
    train_dataset = AdaptiveSpotDataset(
        train_imgs, train_masks,
        transform=get_transforms(patch_size=patch_size),
        patch_size=patch_size
    )
    val_dataset = AdaptiveSpotDataset(
        val_imgs, val_masks,
        transform=None,
        patch_size=patch_size
    )

    # Weighted sampler for spot density balancing
    sampler = WeightedRandomSampler(
        train_dataset.weights,
        num_samples=len(train_dataset),
        replacement=True
    )

    # DataLoaders
    train_loader = DataLoader(
        train_dataset,
        batch_size=batch_size,
        sampler=sampler,
        num_workers=num_workers,
        pin_memory=True,
        drop_last=True,
        prefetch_factor=2,
        persistent_workers=True
    )

    val_loader = DataLoader(
        val_dataset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=num_workers,
        pin_memory=True,
        drop_last=False,
        prefetch_factor=2,
        persistent_workers=True
    )

    return train_loader, val_loader

# # ====================
# # POST-PROCESSING
# # ====================
# from skimage.feature import peak_local_max




# def extract_precise_spots(centroid_map, semantic_mask, min_distance=2, threshold=0.3):
#     """
#     Extract spots from the centroid map with precise localization.
#     centroid_map: 2D numpy array (predicted or GT)
#     semantic_mask: 2D numpy array (predicted or GT)
#     """
#     spots = []
#     combined = centroid_map * (semantic_mask > 0.5)
#     if combined.max() > threshold:
#         coordinates = peak_local_max(
#             combined,
#             min_distance=min_distance,
#             threshold_abs=threshold
#         )
#         for y, x in coordinates:
#             confidence = combined[y, x]
#             spots.append([x, y, confidence])
#     return spots


# # ====================
# # TRAINING UTILITIES
# # ====================
# def get_transforms(patch_size=128):
#     """Get augmentation transforms"""
#     return A.Compose([
#         A.RandomRotate90(p=0.5),
#         A.HorizontalFlip(p=0.5),
#         A.VerticalFlip(p=0.5),
#         A.RandomBrightnessContrast(brightness_limit=0.2, contrast_limit=0.2, p=0.5),
#         A.GaussianBlur(blur_limit=3, p=0.3),
#         A.OneOf([
#             A.ElasticTransform(alpha=1, sigma=50, alpha_affine=50, p=0.5),
#             A.GridDistortion(p=0.5),
#             A.OpticalDistortion(p=0.5),
#         ], p=0.3),
#     ], additional_targets={'flow': 'mask'})

# def create_data_loaders(image_paths, mask_paths, batch_size=8, patch_size=128):
#     """Create optimized data loaders"""
#     # Split data
#     train_imgs, val_imgs, train_masks, val_masks = train_test_split(
#         image_paths, mask_paths, test_size=0.2, random_state=42
#     )
    
#     # Create datasets
#     train_dataset = AdaptiveSpotDataset(
#         train_imgs, train_masks, 
#         transform=get_transforms(patch_size), 
#         patch_size=patch_size
#     )
    
#     val_dataset = AdaptiveSpotDataset(
#         val_imgs, val_masks,
#         transform=None,
#         patch_size=patch_size
#     )
    
#     # Create weighted sampler for training
#     sampler = WeightedRandomSampler(
#         train_dataset.weights, 
#         len(train_dataset),
#         replacement=True
#     )
    
#     # Create data loaders
#     train_loader = DataLoader(
#         train_dataset, 
#         batch_size=batch_size, 
#         sampler=sampler,
#         num_workers=4,
#         pin_memory=True
#     )
    
#     val_loader = DataLoader(
#         val_dataset,
#         batch_size=batch_size,
#         shuffle=False,
#         num_workers=4,
#         pin_memory=True
#     )
    
#     return train_loader, val_loader



### FROM CHAT 7/11/2025
def train_model(model, train_loader, val_loader, num_epochs=100, device='cuda'):
    criterion = AdaptiveSpotLoss()
    optimizer = torch.optim.AdamW(model.parameters(), lr=1e-4, weight_decay=1e-5)
    scheduler = torch.optim.lr_scheduler.CosineAnnealingWarmRestarts(
        optimizer, T_0=10, T_mult=2, eta_min=1e-6
    )
    model.to(device)
    best_loss = float('inf')

    for epoch in range(num_epochs):
        model.train()
        train_losses = []

        for batch_idx, (images, targets, flow_targets, confidence) in enumerate(train_loader):
            images = images.to(device)
            targets = targets.to(device)
            flow_targets = flow_targets.to(device)
            confidence = confidence.to(device)

            optimizer.zero_grad()
            outputs = model(images)
            loss, loss_dict = criterion(outputs, targets, flow_targets, confidence)

            if torch.isnan(loss):
                print(f"⚠️ NaN at epoch {epoch}, batch {batch_idx}")
                continue

            loss.backward()
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            optimizer.step()

            train_losses.append(loss.item())
            if batch_idx % 10 == 0:
                print(f"[Epoch {epoch} | Batch {batch_idx}] Total: {loss.item():.4f} | "
                      f"Centroid: {loss_dict['centroid']:.3f} | DeepFlow: {loss_dict['deep_flow']:.3f}")

        # -------- Validation --------
        model.eval()
        val_losses = []
        with torch.no_grad():
            for images, targets, flow_targets, confidence in val_loader:
                images = images.to(device)
                targets = targets.to(device)
                flow_targets = flow_targets.to(device)
                confidence = confidence.to(device)

                outputs = model(images)
                loss, _ = criterion(outputs, targets, flow_targets, confidence)

                if not torch.isnan(loss):
                    val_losses.append(loss.item())

        # -------- Logging --------
        if train_losses and val_losses:
            avg_train = np.mean(train_losses)
            avg_val = np.mean(val_losses)
            print(f"[Epoch {epoch}] ▶ Train: {avg_train:.4f} | Val: {avg_val:.4f}")

            if avg_val < best_loss:
                best_loss = avg_val
                torch.save(model.state_dict(), 'best_spot_detector.pth')
                print(f"✅ Saved best model with val loss: {avg_val:.4f}")

        scheduler.step()

    return model

# ====================
# INFERENCE (CENTROID MAP)
# ====================
def refine_with_flow(spots: np.ndarray,
                     flow: np.ndarray,
                     n_iters: int = 5,
                     step_size: float = 1.0) -> np.ndarray:
    """
    Refine spot locations by following the local flow field.
    
    Args:
        spots: (N, 3) array of (y, x, score).
        flow:  (2, H, W) array where flow[0]=dy, flow[1]=dx.
        n_iters: how many integration steps to take.
        step_size: scaling of each flow step.
    
    Returns:
        (N, 3) array of refined (y, x, score).
    """
    H, W = flow.shape[1:]
    refined = np.zeros_like(spots, dtype=float)
    
    for i, (y, x, score) in enumerate(spots):
        yf, xf = float(y), float(x)
        for _ in range(n_iters):
            iy, ix = int(round(yf)), int(round(xf))
            iy = np.clip(iy, 0, H-1)
            ix = np.clip(ix, 0, W-1)
            dy, dx = flow[0, iy, ix], flow[1, iy, ix]
            yf += step_size * dy
            xf += step_size * dx
        refined[i] = (yf, xf, score)
    
    return refined

def inference_with_nms(model, image: np.ndarray, device='cuda',
                       min_distance=2, threshold=0.3,
                       flow_iters=5, flow_step=1.0) -> dict:
    model.eval()
    with torch.no_grad():
        if isinstance(image, np.ndarray):
            image = image[None, None] if image.ndim == 2 else image[None]
            img_t = torch.from_numpy(image).float().to(device)
        else:
            img_t = image.to(device).float()

        if img_t.max() > 1.0:
            img_t = img_t / 255.0

        out = model(img_t)
        sem_map = torch.sigmoid(out['semantic'])[0, 0].cpu().numpy()
        centroids = torch.sigmoid(out['heatmaps'])[0, 1].cpu().numpy()
        flow = out['flow'][0].cpu().numpy()

        spots = extract_precise_spots(
            centroid_map=centroids,
            semantic_mask=sem_map,
            flow=flow,
            min_distance=min_distance,
            threshold=threshold,
            flow_iters=flow_iters,
            flow_step=flow_step
        )

        return {
            'spots': np.array(spots),
            'semantic': sem_map,
            'centroid_map': centroids,
            'flow': flow
        }
# ====================
# EVALUATION (CENTROID MAP)
# ====================
def evaluate_model(model, test_loader, device='cuda'):
    model.eval()
    all_p, all_r, all_f = [], [], []

    with torch.no_grad():
        for images, targets, flow_targets, _ in test_loader:
            images = images.to(device)

            for i in range(images.shape[0]):
                img = images[i:i+1]
                out = model(img)

                pred_sem = torch.sigmoid(out['semantic'])[0, 0].cpu().numpy()
                pred_cmap = torch.sigmoid(out['heatmaps'])[0, 1].cpu().numpy()
                pred_flow = out['flow'][0].cpu().numpy()

                pred_spots = extract_precise_spots(
                    pred_cmap, pred_sem,
                    flow=pred_flow,
                    min_distance=2, threshold=0.3, flow_iters=5, flow_step=1.0
                )

                gt_cmap = targets[i, 4].cpu().numpy()
                gt_sem = targets[i, 0].cpu().numpy()
                gt_spots = extract_precise_spots(
                    gt_cmap, gt_sem,
                    min_distance=2, threshold=0.3,
                    flow=None
                )

                p, r, f = calculate_detection_metrics(pred_spots, gt_spots, distance_threshold=3)
                all_p.append(p); all_r.append(r); all_f.append(f)

    return {
        'mean_precision': np.mean(all_p),
        'mean_recall':    np.mean(all_r),
        'mean_f1':        np.mean(all_f),
        'std_precision':  np.std(all_p),
        'std_recall':     np.std(all_r),
        'std_f1':         np.std(all_f),
    }
# ====================
# SPOT DISPLAY (CORRECT COORDINATES)
# ====================
import matplotlib.pyplot as plt

def display_spots_on_image(image, spots, title="Detected Spots", color='r'):
    plt.figure(figsize=(6, 6))
    plt.imshow(image, cmap='gray')
    if len(spots) > 0:
        spots = np.array(spots)
        plt.scatter(spots[:, 0], spots[:, 1], c=color, s=30, marker='o',
                    edgecolors='w', linewidths=1.5, label='Detected')
    plt.title(title)
    plt.axis('off')
    plt.legend()
    plt.show()

# ====================
# FIXED METRICS
# ====================

def calculate_detection_metrics(predictions, ground_truth, distance_threshold=3):
    """Calculate precision, recall, and F1 for spot detection"""
    if len(predictions) == 0 and len(ground_truth) == 0:
        return 1.0, 1.0, 1.0  # Perfect if both empty
    
    if len(predictions) == 0:
        return 0.0, 0.0, 0.0  # No detections
    
    if len(ground_truth) == 0:
        return 0.0, 1.0, 0.0  # False positives only
    
    # Convert to numpy arrays
    pred_points = np.array(predictions)
    gt_points = np.array(ground_truth)
    
    # Handle different input formats
    if pred_points.shape[1] >= 2:
        pred_points = pred_points[:, :2]  # Take only x, y coordinates
    if gt_points.shape[1] >= 2:
        gt_points = gt_points[:, :2]
    
    # Calculate distances between all predictions and ground truth
    distances = cdist(pred_points, gt_points)
    
    # Find matches within distance threshold
    matches = distances <= distance_threshold
    
    # Calculate metrics
    true_positives = np.sum(np.any(matches, axis=1))
    false_positives = len(predictions) - true_positives
    false_negatives = len(ground_truth) - np.sum(np.any(matches, axis=0))
    
    precision = true_positives / (true_positives + false_positives) if (true_positives + false_positives) > 0 else 0
    recall = true_positives / (true_positives + false_negatives) if (true_positives + false_negatives) > 0 else 0
    f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0
    
    return precision, recall, f1



## FROM AUGMENT 7/13/2025
# ====================
# TRAINING LOOP
# ====================
def train_model(model, train_loader, val_loader, num_epochs=100, device='cuda'):
    """Training loop with advanced optimization"""
    criterion = AdaptiveSpotLoss()
    optimizer = torch.optim.AdamW(model.parameters(), lr=1e-4, weight_decay=1e-5)
    scheduler = torch.optim.lr_scheduler.CosineAnnealingWarmRestarts(
        optimizer, T_0=10, T_mult=2, eta_min=1e-6
    )
    model.to(device)
    best_loss = float('inf')
    for epoch in range(num_epochs):
        model.train()
        train_losses = []
        for batch_idx, (images, targets, flow_targets, confidence) in enumerate(train_loader):
            images = images.to(device)
            targets = targets.to(device)
            flow_targets = flow_targets.to(device)
            confidence = confidence.to(device)
            optimizer.zero_grad()
            outputs = model(images)
            loss, loss_dict = criterion(outputs, targets, flow_targets, confidence)
            if torch.isnan(loss):
                print(f"NaN loss detected at epoch {epoch}, batch {batch_idx}")
                continue
            loss.backward()
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            optimizer.step()
            train_losses.append(loss.item())
            if batch_idx % 10 == 0:
                print(f'Epoch {epoch}, Batch {batch_idx}, Loss: {loss.item():.4f}')
                for k, v in loss_dict.items():
                    if k != 'total':
                        print(f'  {k}: {v:.4f}')
        model.eval()
        val_losses = []
        with torch.no_grad():
            for images, targets, flow_targets, confidence in val_loader:
                images = images.to(device)
                targets = targets.to(device)
                flow_targets = flow_targets.to(device)
                confidence = confidence.to(device)
                outputs = model(images)
                loss, _ = criterion(outputs, targets, flow_targets, confidence)
                if not torch.isnan(loss):
                    val_losses.append(loss.item())
        if len(train_losses) > 0 and len(val_losses) > 0:
            avg_train_loss = np.mean(train_losses)
            avg_val_loss = np.mean(val_losses)
            print(f'Epoch {epoch}: Train Loss: {avg_train_loss:.4f}, Val Loss: {avg_val_loss:.4f}')
            if avg_val_loss < best_loss:
                best_loss = avg_val_loss
                torch.save(model.state_dict(), 'best_spot_detector.pth')
                print(f"New best model saved with validation loss: {avg_val_loss:.4f}")
        scheduler.step()
    return model

# ====================
# INFERENCE (CENTROID MAP)
# ====================
def refine_with_flow(spots: np.ndarray,
                     flow: np.ndarray,
                     n_iters: int = 5,
                     step_size: float = 1.0) -> np.ndarray:
    """
    Refine spot locations by following the local flow field.
    
    Args:
        spots: (N, 3) array of (y, x, score).
        flow:  (2, H, W) array where flow[0]=dy, flow[1]=dx.
        n_iters: how many integration steps to take.
        step_size: scaling of each flow step.
    
    Returns:
        (N, 3) array of refined (y, x, score).
    """
    H, W = flow.shape[1:]
    refined = np.zeros_like(spots, dtype=float)
    
    for i, (y, x, score) in enumerate(spots):
        yf, xf = float(y), float(x)
        for _ in range(n_iters):
            iy, ix = int(round(yf)), int(round(xf))
            iy = np.clip(iy, 0, H-1)
            ix = np.clip(ix, 0, W-1)
            dy, dx = flow[0, iy, ix], flow[1, iy, ix]
            yf += step_size * dy
            xf += step_size * dx
        refined[i] = (yf, xf, score)
    
    return refined

def inference_with_nms(model,
                       image: np.ndarray,
                       device: str = 'cuda',
                       nms_threshold: float = 0.3,
                       flow_iters: int = 5,
                       flow_step: float = 1.0) -> dict:
    """
    Run model, NMS on centroid map, then refine via the flow field.
    
    Returns:
        {
          'spots': (M,3) array of (y, x, score),
          'semantic':  HxW semantic mask,
          'centroid_map': HxW raw centroid heatmap,
          'flow':        2xHxW flow field
        }
    """
    model.eval()
    with torch.no_grad():
        # Prepare tensor
        if isinstance(image, np.ndarray):
            if image.ndim == 2:
                image = image[None, None, ...]
            elif image.ndim == 3:
                image = image[None, ...]
            image = torch.from_numpy(image).float().to(device)
        else:
            image = image.to(device).float()
        
        # normalize if needed
        if image.max() > 1.0:
            image = image / 255.0
        
        # forward
        outputs = model(image)
        sem = torch.sigmoid(outputs['semantic'])[0, 0].cpu().numpy()
        cmap = torch.sigmoid(outputs['heatmaps'])[0, 1].cpu().numpy()
        flow = outputs['flow'][0].cpu().numpy()
        
        # initial spot extraction
        raw_spots = extract_precise_spots(cmap, sem,
                                          min_distance=2,
                                          threshold=0.3)
        if len(raw_spots) == 0:
            return {'spots': np.empty((0,3)),
                    'semantic': sem,
                    'centroid_map': cmap,
                    'flow': flow}
        
        spots = np.array(raw_spots)  # (N,3)
        
        # non-maximum suppression
        scores = spots[:, 2]
        order = scores.argsort()[::-1]
        keep = []
        while order.size:
            i = order[0]
            keep.append(i)
            if order.size == 1:
                break
            current = spots[i, :2]
            others = spots[order[1:], :2]
            dists = np.linalg.norm(others - current, axis=1)
            order = order[1:][dists > nms_threshold]
        
        nms_spots = spots[keep]
        
        # refine with flow
        refined = refine_with_flow(nms_spots,
                                   flow,
                                   n_iters=flow_iters,
                                   step_size=flow_step)
        
        return {
            'spots': refined,
            'semantic': sem,
            'centroid_map': cmap,
            'flow': flow
        }
# ====================
# EVALUATION (CENTROID MAP)
# ====================
def evaluate_model(model, test_loader, device='cuda'):
    """Comprehensive model evaluation (centroid map)"""
    model.eval()
    all_precisions = []
    all_recalls = []
    all_f1s = []
    with torch.no_grad():
        for batch_data in test_loader:
            if len(batch_data) == 4:
                images, targets, _, _ = batch_data
            else:
                images, targets = batch_data[:2]
            images = images.to(device)
            batch_size = images.shape[0]
            for i in range(batch_size):
                try:
                    img = images[i:i+1]
                    outputs = model(img)
                    semantic = torch.sigmoid(outputs['semantic'])[0, 0].cpu().numpy()
                    centroid_pred = torch.sigmoid(outputs['heatmaps'])[0, 1].cpu().numpy()
                    predicted_spots = extract_precise_spots(centroid_pred, semantic)
                    gt_centroid = targets[i, 4].cpu().numpy()  # Channel 4: centroid map
                    gt_semantic = targets[i, 0].cpu().numpy()
                    gt_spots = extract_precise_spots(gt_centroid, gt_semantic)
                    precision, recall, f1 = calculate_detection_metrics(
                        predicted_spots, gt_spots, distance_threshold=3
                    )
                    all_precisions.append(precision)
                    all_recalls.append(recall)
                    all_f1s.append(f1)
                except Exception as e:
                    print(f"Error processing batch {i}: {e}")
                    continue
    if len(all_precisions) == 0:
        return {
            'mean_precision': 0.0,
            'mean_recall': 0.0,
            'mean_f1': 0.0,
            'std_precision': 0.0,
            'std_recall': 0.0,
            'std_f1': 0.0
        }
    return {
        'mean_precision': np.mean(all_precisions),
        'mean_recall': np.mean(all_recalls),
        'mean_f1': np.mean(all_f1s),
        'std_precision': np.std(all_precisions),
        'std_recall': np.std(all_recalls),
        'std_f1': np.std(all_f1s)
    }

# ====================
# SPOT DISPLAY (CORRECT COORDINATES)
# ====================
import matplotlib.pyplot as plt

def display_spots_on_image(image, spots, title="Detected Spots", color='r'):
    """
    Display detected spots on the image.
    image: 2D numpy array
    spots: Nx3 array (x, y, confidence)
    """
    plt.figure(figsize=(6, 6))
    plt.imshow(image, cmap='gray')
    if len(spots) > 0:
        spots = np.array(spots)
        plt.scatter(spots[:, 0], spots[:, 1], c=color, s=30, marker='o', edgecolors='w', linewidths=1.5, label='Detected')
    plt.title(title)
    plt.axis('off')
    plt.legend()
    plt.show()

# ====================
# FIXED METRICS
# ====================

def calculate_detection_metrics(predictions, ground_truth, distance_threshold=3):
    """Calculate precision, recall, and F1 for spot detection"""
    if len(predictions) == 0 and len(ground_truth) == 0:
        return 1.0, 1.0, 1.0  # Perfect if both empty
    
    if len(predictions) == 0:
        return 0.0, 0.0, 0.0  # No detections
    
    if len(ground_truth) == 0:
        return 0.0, 1.0, 0.0  # False positives only
    
    # Convert to numpy arrays
    pred_points = np.array(predictions)
    gt_points = np.array(ground_truth)
    
    # Handle different input formats
    if pred_points.shape[1] >= 2:
        pred_points = pred_points[:, :2]  # Take only x, y coordinates
    if gt_points.shape[1] >= 2:
        gt_points = gt_points[:, :2]
    
    # Calculate distances between all predictions and ground truth
    distances = cdist(pred_points, gt_points)
    
    # Find matches within distance threshold
    matches = distances <= distance_threshold
    
    # Calculate metrics
    true_positives = np.sum(np.any(matches, axis=1))
    false_positives = len(predictions) - true_positives
    false_negatives = len(ground_truth) - np.sum(np.any(matches, axis=0))
    
    precision = true_positives / (true_positives + false_positives) if (true_positives + false_positives) > 0 else 0
    recall = true_positives / (true_positives + false_negatives) if (true_positives + false_negatives) > 0 else 0
    f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0
    
    return precision, recall, f1




### FROM CHAT 7/11/2025

import os
import glob
from pathlib import Path
import tifffile
import matplotlib.pyplot as plt
import numpy as np

# Set your image and mask directories
image_dir = "/mnt/d/Users/<USER>/FISH_spots/2d/synthetic/images/"  # <-- CHANGE THIS
mask_dir = "/mnt/d/Users/<USER>/FISH_spots/2d/synthetic/masks/"    # <-- CHANGE THIS

image_paths = sorted(glob.glob(os.path.join(image_dir, '*.tif')))
mask_paths = sorted(glob.glob(os.path.join(mask_dir, '*.tif')))

# Set model output directory
model_dir = '/mnt/d/Users/<USER>/Documents/spot detector git amazon 2025/training_dataset/spotmodel_claude_centroid_soft_blurred7112025'
os.makedirs(model_dir, exist_ok=True)
print(f"Model and checkpoints will be saved to: {model_dir}")

# --- TRAINING AND VISUALIZATION ---
from tqdm import tqdm

def plot_training_progress(image, pred_semantic, pred_centroid, pred_flow, detected_spots, epoch, save_dir):
    fig, axes = plt.subplots(1, 4, figsize=(20, 5))

    axes[0].imshow(image, cmap='gray')
    axes[0].set_title('Input Image')
    axes[0].axis('off')

    axes[1].imshow(pred_semantic, cmap='gray')
    axes[1].set_title('Predicted Semantic')
    axes[1].axis('off')

    axes[2].imshow(pred_centroid, cmap='hot')
    axes[2].set_title('Predicted Centroid Map')
    axes[2].axis('off')

    # Overlay detected spots
    if len(detected_spots) > 0:
        axes[2].scatter(detected_spots[:, 0], detected_spots[:, 1], c='red', s=20, marker='x', label='Detected')
        axes[2].legend()

    # Flow field visualization
    step = max(1, pred_flow.shape[1] // 32)
    Y, X = np.mgrid[0:pred_flow.shape[1], 0:pred_flow.shape[2]]
    axes[3].imshow(image, cmap='gray', alpha=0.7)
    axes[3].quiver(
        X[::step, ::step], Y[::step, ::step],
        pred_flow[1, ::step, ::step], pred_flow[0, ::step, ::step],
        color='cyan', angles='xy', scale_units='xy', scale=1, width=0.003
    )
    axes[3].set_title('Predicted Flow Field')
    axes[3].axis('off')

    plt.suptitle(f'Epoch {epoch} QC')
    plt.tight_layout()

    if save_dir:
        os.makedirs(save_dir, exist_ok=True)
        outpath = os.path.join(save_dir, f'qc_epoch_{epoch:03d}.png')
        plt.savefig(outpath, dpi=150)

    plt.show()

def notebook_train_model(model, train_loader, val_loader,
                         num_epochs=100, device='cuda', model_dir=model_dir):
    criterion = AdaptiveSpotLoss()
    optimizer = torch.optim.AdamW([
        {'params': model.stem.parameters(),        'lr': 1e-4},
        {'params': [p for n, p in model.named_parameters() if 'enc' in n],  'lr': 1e-4},
        {'params': [p for n, p in model.named_parameters() if 'dec' in n],  'lr': 2e-4},
        {'params': [p for n, p in model.named_parameters() if 'head' in n], 'lr': 3e-4},
        {'params': model.aspp.parameters(),       'lr': 2e-4},
        {'params': model.se.parameters(),         'lr': 1e-4},
    ], weight_decay=1e-5)

    scheduler = torch.optim.lr_scheduler.CosineAnnealingWarmRestarts(
        optimizer, T_0=10, T_mult=2, eta_min=1e-6
    )

    model.to(device)
    best_loss = float('inf')
    train_losses, val_losses = [], []
    vis_dir = os.path.join(model_dir, 'progress')
    os.makedirs(vis_dir, exist_ok=True)

    for epoch in range(num_epochs):
        # -------- TRAIN --------
        model.train()
        epoch_train = []

        for batch_idx, (imgs, targets, flow_tgt, conf) in enumerate(
                tqdm(train_loader, desc=f"Epoch {epoch}")):
            imgs = imgs.to(device)
            targets = targets.to(device)
            flow_tgt = flow_tgt.to(device)
            conf = conf.to(device)

            optimizer.zero_grad()
            outputs = model(imgs)
            loss, loss_dict = criterion(outputs, targets, flow_tgt, conf)
            loss.backward()
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            optimizer.step()

            epoch_train.append(loss.item())
            if batch_idx % 10 == 0:
                print(f"[Epoch {epoch:02d} | Batch {batch_idx:03d}] "
                      f"Loss={loss.item():.4f} | Centroid={loss_dict['centroid']:.3f} | Flow={loss_dict['deep_flow']:.3f}")

        train_losses.append(np.mean(epoch_train))

        # -------- VALIDATE --------
        model.eval()
        epoch_val = []

        with torch.no_grad():
            for imgs, targets, flow_tgt, conf in val_loader:
                imgs = imgs.to(device)
                targets = targets.to(device)
                flow_tgt = flow_tgt.to(device)
                conf = conf.to(device)

                outputs = model(imgs)
                loss, _ = criterion(outputs, targets, flow_tgt, conf)
                epoch_val.append(loss.item())

        val_losses.append(np.mean(epoch_val))
        print(f"✅ Epoch {epoch:02d} | Train={train_losses[-1]:.4f} | Val={val_losses[-1]:.4f}")

        # -------- SAVE BEST --------
        if val_losses[-1] < best_loss:
            best_loss = val_losses[-1]
            torch.save(model.state_dict(), os.path.join(model_dir, 'best_spot_detector.pth'))
            print(f"📦 Best model saved (val={best_loss:.4f})")

        scheduler.step()

        # -------- QC PLOTS every 5 epochs --------
        if epoch % 5 == 0:
            imgs, targets, flow_tgt, _ = next(iter(val_loader))
            imgs = imgs.to(device)
            targets = targets.to(device)
            out = model(imgs)

            sem = torch.sigmoid(out['semantic'])[0, 0].detach().cpu().numpy()
            cmap = torch.sigmoid(out['heatmaps'])[0, 1].detach().cpu().numpy()
            flow = out['flow'][0].detach().cpu().numpy()
            img0 = imgs[0, 0].detach().cpu().numpy()

            spots = extract_precise_spots(
                centroid_map=cmap,
                semantic_mask=sem,
                flow=flow,
                min_distance=2,
                threshold=0.3,
                flow_iters=5,
                flow_step=1.0
            )

            plot_training_progress(
                image=img0,
                pred_semantic=sem,
                pred_centroid=cmap,
                pred_flow=flow,
                detected_spots=np.array(spots),
                epoch=epoch,
                save_dir=vis_dir
            )

    # -------- Final loss plot --------
    plt.figure()
    plt.plot(train_losses, label='Train Loss')
    plt.plot(val_losses,   label='Val Loss')
    plt.xlabel('Epoch'); plt.ylabel('Loss')
    plt.legend(); plt.title('Loss Curves')
    plt.savefig(os.path.join(model_dir, 'loss_curve.png'), dpi=150)
    plt.show()

    return model

# # --- SETUP: Specify your data and output paths here ---
# import os
# import glob
# from pathlib import Path
# import tifffile
# import matplotlib.pyplot as plt
# import numpy as np

# # Set your image and mask directories
# image_dir = "/mnt/d/Users/<USER>/FISH_spots/2d/synthetic/images/"  # <-- CHANGE THIS
# mask_dir = "/mnt/d/Users/<USER>/FISH_spots/2d/synthetic/masks/"    # <-- CHANGE THIS

# image_paths = sorted(glob.glob(os.path.join(image_dir, '*.tif')))
# mask_paths = sorted(glob.glob(os.path.join(mask_dir, '*.tif')))

# # Set model output directory
# model_dir = '/mnt/d/Users/<USER>/Documents/spot detector git amazon 2025/training_dataset/spotmodel_claude_centroid_soft_blurred7112025'
# os.makedirs(model_dir, exist_ok=True)
# print(f"Model and checkpoints will be saved to: {model_dir}")

# # --- TRAINING AND VISUALIZATION ---
# from tqdm import tqdm

# def plot_training_progress(image,
#                            pred_semantic,
#                            pred_centroid,
#                            pred_flow,
#                            detected_spots,
#                            epoch,
#                            save_dir):
#     """
#     Visualize input, semantic, masked centroid, flow, and refined spots.
#     """
#     # Create a masked centroid: only inside semantic>0.5
#     sem_mask = (pred_semantic > 0.5).astype(float)
#     masked_centroid = pred_centroid * sem_mask

#     fig, axes = plt.subplots(1, 4, figsize=(20, 5))
#     # 1) Input
#     axes[0].imshow(image, cmap='gray')
#     axes[0].set_title('Input Image')
#     axes[0].axis('off')

#     # 2) Semantic
#     axes[1].imshow(pred_semantic, cmap='gray')
#     axes[1].set_title('Predicted Semantic')
#     axes[1].axis('off')

#     # 3) Centroid (masked & overlaid on semantic)
#     axes[2].imshow(sem_mask, cmap='gray')                      # background semantic
#     axes[2].imshow(masked_centroid, cmap='hot', alpha=0.7)     # hot centroids
#     axes[2].set_title('Centroid Map ⨯ Semantic')
#     axes[2].axis('off')
#     if len(detected_spots) > 0:
#         ys, xs = detected_spots[:,0], detected_spots[:,1]
#         axes[2].scatter(xs, ys, c='cyan', s=30, marker='x', label='Refined')
#         axes[2].legend(loc='upper right')

#     # 4) Flow quiver
#     step = max(1, pred_flow.shape[1] // 32)
#     Y, X = np.mgrid[0:pred_flow.shape[1], 0:pred_flow.shape[2]]
#     axes[3].imshow(image, cmap='gray', alpha=0.7)
#     axes[3].quiver(
#         X[::step, ::step], Y[::step, ::step],
#         pred_flow[1, ::step, ::step], pred_flow[0, ::step, ::step],
#         color='cyan', angles='xy', scale_units='xy', scale=1, width=0.003
#     )
#     axes[3].set_title('Predicted Flow Field')
#     axes[3].axis('off')

#     plt.suptitle(f'Epoch {epoch} QC')
#     plt.tight_layout()

#     # save
#     if save_dir is not None:
#         os.makedirs(save_dir, exist_ok=True)
#         outpath = os.path.join(save_dir, f'qc_epoch_{epoch}.png')
#         plt.savefig(outpath, dpi=150)

#     plt.show()


# # --- NOTEBOOK-FRIENDLY TRAINING LOOP ---
# def notebook_train_model(model, train_loader, val_loader, num_epochs=100, device='cuda', model_dir=model_dir):
#     criterion = AdaptiveSpotLoss()
#     optimizer = torch.optim.AdamW([
#         {'params': model.stem.parameters(), 'lr': 1e-4},
#         {'params': [p for n, p in model.named_parameters() if 'enc' in n], 'lr': 1e-4},
#         {'params': [p for n, p in model.named_parameters() if 'dec' in n], 'lr': 2e-4},
#         {'params': [p for n, p in model.named_parameters() if 'head' in n], 'lr': 3e-4},
#         {'params': model.aspp.parameters(), 'lr': 2e-4},
#         {'params': model.se.parameters(), 'lr': 1e-4},
#     ], weight_decay=1e-5)
#     scheduler = torch.optim.lr_scheduler.CosineAnnealingWarmRestarts(
#         optimizer, T_0=10, T_mult=2, eta_min=1e-6
#     )
#     model.to(device)
#     best_loss = float('inf')
#     train_losses, val_losses = [], []
#     vis_dir = os.path.join(model_dir, 'progress')
#     os.makedirs(vis_dir, exist_ok=True)
#     for epoch in range(num_epochs):
#         model.train()
#         epoch_train_losses = []
#         for batch_idx, (images, targets, flow_targets, confidence) in enumerate(tqdm(train_loader, desc=f'Epoch {epoch}')):
#             images = images.to(device)
#             targets = targets.to(device)
#             flow_targets = flow_targets.to(device)
#             confidence = confidence.to(device)
#             optimizer.zero_grad()
#             outputs = model(images)
#             loss, loss_dict = criterion(outputs, targets, flow_targets, confidence)
#             loss.backward()
#             torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
#             optimizer.step()
#             epoch_train_losses.append(loss.item())
#         train_losses.append(np.mean(epoch_train_losses))
#         # Validation
#         model.eval()
#         epoch_val_losses = []
#         with torch.no_grad():
#             for images, targets, flow_targets, confidence in val_loader:
#                 images = images.to(device)
#                 targets = targets.to(device)
#                 flow_targets = flow_targets.to(device)
#                 confidence = confidence.to(device)
#                 outputs = model(images)
#                 loss, _ = criterion(outputs, targets, flow_targets, confidence)
#                 epoch_val_losses.append(loss.item())
#         val_losses.append(np.mean(epoch_val_losses))
#         print(f'Epoch {epoch}: Train Loss: {train_losses[-1]:.4f}, Val Loss: {val_losses[-1]:.4f}')
#         # Save best model
#         if val_losses[-1] < best_loss:
#             best_loss = val_losses[-1]
#             torch.save(model.state_dict(), os.path.join(model_dir, 'best_spot_detector.pth'))
#         scheduler.step()
#         # Visualization every 5 epochs
#         if epoch % 5 == 0:
#             images, targets, _, _ = next(iter(val_loader))
#             images = images.to(device)
#             outputs = model(images)
#             pred_semantic = torch.sigmoid(outputs['semantic'][0, 0]).detach().cpu().numpy()
#             pred_centroid_map = torch.sigmoid(outputs['heatmaps'][0,1]).detach().cpu().numpy()  # Channel 1: centroid map
#             pred_flow = outputs['flow'][0].detach().cpu().numpy()
#             gt_instance_mask = targets[0,3].detach().cpu().numpy()  # Channel 3: soft-blurred instance mask
#             input_img = images[0,0].detach().cpu().numpy()
#             semantic = torch.sigmoid(outputs['semantic'][0,0]).detach().cpu().numpy()
#             mask = (semantic > 0.3).astype(np.float32)
#             from skimage.feature import peak_local_max
#             peaks = peak_local_max(pred_centroid_map * mask, min_distance=2, threshold_abs=0.3)
#             plot_training_progress(
#                 image=input_img,
#                 pred_semantic=pred_semantic,
#                 pred_centroid=pred_centroid_map,
#                 pred_flow=pred_flow,
#                 detected_spots=peaks,
#                 epoch=epoch,
#                 save_dir=vis_dir
#             )
#     plt.figure()
#     plt.plot(train_losses, label='Train Loss')
#     plt.plot(val_losses, label='Val Loss')
#     plt.xlabel('Epoch')
#     plt.ylabel('Loss')
#     plt.legend()
#     plt.title('Training/Validation Loss')
#     plt.savefig(os.path.join(model_dir, 'loss_curve.png'), dpi=150)
#     plt.show()
#     return model


# --- RUN TRAINING ---
# Initialize model
model = AdaptiveSpotDetector(in_ch=1, num_heatmaps=2, base_ch=32)

# Create data loaders
train_loader, val_loader = create_data_loaders(
    image_paths, mask_paths, batch_size=32, patch_size=128
)

# Train model (change num_epochs as needed)
trained_model = notebook_train_model(model, train_loader, val_loader, num_epochs=400, device=device, model_dir=model_dir)

# Example inference (uncomment and set test image path)
# test_image = tifffile.imread('/absolute/path/to/test/image.tif')
# results = inference_with_nms(trained_model, test_image, device=device)
# visualize_results(test_image, results, os.path.join(model_dir, 'detection_results.png'))

import matplotlib.pyplot as plt
import numpy as np
import tifffile
import cv2
import torch
from torch.utils.data import Dataset
from skimage.measure import regionprops
from scipy.spatial.distance import cdist
from scipy.ndimage import distance_transform_edt

class AdaptiveSpotDataset(Dataset):
    def __init__(self, image_paths, mask_paths, transform=None, patch_size=128, sparse_threshold=0.01):
        assert len(image_paths) == len(mask_paths)
        self.image_paths = image_paths
        self.mask_paths = mask_paths
        self.transform = transform
        self.patch_size = patch_size
        self.sparse_threshold = sparse_threshold
        self.weights = self._calculate_weights()

    def _calculate_weights(self):
        weights = []
        for mp in self.mask_paths:
            try:
                m = tifffile.imread(mp)
                num_inst = max(0, len(np.unique(m)) - 1)
                area = m.shape[0] * m.shape[1]
                density = num_inst * (self.patch_size ** 2) / (area + 1e-8)
                weights.append(1.0 / (density + self.sparse_threshold))
            except Exception as e:
                print(f"Error calculating weights: {e}")
                weights.append(1.0)
        w = torch.DoubleTensor(weights)
        return w / w.sum() * len(w)

    def _get_centroids_and_sizes(self, mask):
        centroids = []
        spot_sizes = []
        props = regionprops(mask.astype(int))

        for prop in props:
            cy, cx = prop.centroid
            centroids.append([cy, cx])
            spot_sizes.append(prop.equivalent_diameter)

        return centroids, spot_sizes

    def _adaptive_heatmap_generation(self, mask, centroids, spot_sizes):
        h, w = mask.shape
        y_coords, x_coords = np.meshgrid(np.arange(h), np.arange(w), indexing='ij')

        fine_heatmap = np.zeros_like(mask, dtype=np.float32)
        medium_heatmap = np.zeros_like(mask, dtype=np.float32)
        coarse_heatmap = np.zeros_like(mask, dtype=np.float32)

        for (cy, cx), size in zip(centroids, spot_sizes):
            sigma = np.clip(size * 0.4, 0.8, 10.0)
            g = np.exp(-((y_coords - cy) ** 2 + (x_coords - cx) ** 2) / (2 * sigma ** 2))

            if size <= 3:
                fine_heatmap = np.clip(fine_heatmap + g, 0, 1)
            elif size <= 8:
                medium_heatmap = np.clip(medium_heatmap + g, 0, 1)
            else:
                coarse_heatmap = np.clip(coarse_heatmap + g, 0, 1)

        return [fine_heatmap, medium_heatmap, coarse_heatmap]

    def _generate_distance_transform(self, mask):
        semantic = (mask > 0).astype(np.uint8)
        distance = distance_transform_edt(semantic)
        max_possible_distance = np.sqrt(self.patch_size ** 2 + self.patch_size ** 2)
        distance = np.clip(distance / max_possible_distance, 0, 1)
        return distance.astype(np.float32)

    def _generate_flow_field(self, mask, centroids):
        h, w = mask.shape
        y_coords, x_coords = np.meshgrid(np.arange(h), np.arange(w), indexing='ij')

        flow_y = np.zeros((h, w), dtype=np.float32)
        flow_x = np.zeros((h, w), dtype=np.float32)

        if len(centroids) == 0:
            return np.stack([flow_y, flow_x], axis=0)

        centroids_arr = np.array(centroids)
        spot_mask = (mask > 0)

        if not spot_mask.any():
            return np.stack([flow_y, flow_x], axis=0)

        spot_pixels = np.column_stack(np.where(spot_mask))
        distances = cdist(spot_pixels, centroids_arr)
        nearest_centroid_idx = np.argmin(distances, axis=1)

        for i, (py, px) in enumerate(spot_pixels):
            nearest_centroid = centroids_arr[nearest_centroid_idx[i]]
            cy, cx = nearest_centroid

            dy = cy - py
            dx = cx - px
            norm = np.sqrt(dy**2 + dx**2) + 1e-8

            flow_y[py, px] = dy / norm
            flow_x[py, px] = dx / norm

        return np.stack([flow_y, flow_x], axis=0)

    def __len__(self):
        return len(self.image_paths)

    def __getitem__(self, idx):
        empty_img = torch.zeros((1, self.patch_size, self.patch_size), dtype=torch.float32)
        empty_mask = torch.zeros((6, self.patch_size, self.patch_size), dtype=torch.float32)
        empty_flow = torch.zeros((2, self.patch_size, self.patch_size), dtype=torch.float32)
        empty_conf = torch.zeros((self.patch_size, self.patch_size), dtype=torch.float32)

        for attempt in range(10):
            try:
                img = tifffile.imread(self.image_paths[idx]).astype(np.float32)
                msk = tifffile.imread(self.mask_paths[idx]).astype(np.int32)
                img /= (img.max() + 1e-8)
                img = np.clip(img, 0, 1)
                H, W = img.shape[:2]
                x0 = np.random.randint(0, max(0, H - self.patch_size))
                y0 = np.random.randint(0, max(0, W - self.patch_size))

                patch_img = img[x0:x0+self.patch_size, y0:y0+self.patch_size]
                patch_msk = msk[x0:x0+self.patch_size, y0:y0+self.patch_size]

                ph = self.patch_size - patch_img.shape[0]
                pw = self.patch_size - patch_img.shape[1]
                if ph > 0 or pw > 0:
                    patch_img = np.pad(patch_img, ((0, ph), (0, pw)), 'reflect')
                    patch_msk = np.pad(patch_msk, ((0, ph), (0, pw)), 'constant')

                centroids, spot_sizes = self._get_centroids_and_sizes(patch_msk)

                heatmaps = self._adaptive_heatmap_generation(patch_msk, centroids, spot_sizes)
                distance_map = self._generate_distance_transform(patch_msk)
                flow_field = self._generate_flow_field(patch_msk, centroids)

                sem = (patch_msk > 0).astype(np.float32)
                bnd = np.zeros_like(sem, dtype=np.float32)
                for iid in np.unique(patch_msk):
                    if iid == 0: continue
                    im = (patch_msk == iid).astype(np.uint8)
                    kernel = np.ones((2,2), np.uint8)
                    er = cv2.erode(im, kernel, iterations=1)
                    bnd += ((im - er) > 0).astype(np.float32)
                bnd = np.clip(bnd, 0, 1)

                mask_stack = np.stack([sem, bnd, distance_map] + heatmaps, axis=0)

                if self.transform is not None:
                    img_u8 = (patch_img * 255).astype(np.uint8)
                    mask_hwc = np.moveaxis(mask_stack, 0, -1)
                    flow_hwc = np.moveaxis(flow_field, 0, -1)

                    aug = self.transform(image=img_u8, mask=mask_hwc, flow=flow_hwc)
                    patch_img = aug['image'].astype(np.float32) / 255.0
                    mask_stack = np.moveaxis(aug['mask'], -1, 0).astype(np.float32)
                    flow_field = np.moveaxis(aug.get('flow', flow_hwc), -1, 0).astype(np.float32)

                    mask_stack[0] = (mask_stack[0] > 0.5).astype(np.float32)
                    mask_stack[1] = (mask_stack[1] > 0.5).astype(np.float32)

                img_t = torch.from_numpy(patch_img).unsqueeze(0)
                msk_t = torch.from_numpy(mask_stack)
                flow_t = torch.from_numpy(flow_field)

                semantic = mask_stack[0]
                spot_density = (semantic > 0).sum() / (self.patch_size ** 2)

                if spot_density < 0.05:
                    confidence = (semantic > 0).astype(np.float32)
                elif spot_density > 0.3:
                    confidence = np.ones_like(semantic, dtype=np.float32)
                    confidence[mask_stack[1] > 0] *= 2.0
                else:
                    confidence = np.ones_like(semantic, dtype=np.float32)

                confidence_t = torch.from_numpy(confidence)

                if len(centroids) == 0:
                    continue

                return img_t, msk_t, flow_t, confidence_t

            except Exception as e:
                print(f"Error in dataset __getitem__ attempt {attempt}: {e}")
                continue

        return empty_img, empty_mask, empty_flow, empty_conf

def visualize_mask_and_centroids(mask, centroids):
    plt.figure(figsize=(5, 5))
    plt.imshow(mask, cmap='nipy_spectral')
    plt.scatter([c[1] for c in centroids], [c[0] for c in centroids], c='red', s=10)
    plt.title('Mask with Centroids')
    plt.axis('off')
    plt.show()

def visualize_heatmaps(heatmaps):
    fig, axes = plt.subplots(1, 3, figsize=(15, 5))
    axes[0].imshow(heatmaps[0], cmap='hot')
    axes[0].set_title('Fine Heatmap')
    axes[0].axis('off')
    axes[1].imshow(heatmaps[1], cmap='hot')
    axes[1].set_title('Medium Heatmap')
    axes[1].axis('off')
    axes[2].imshow(heatmaps[2], cmap='hot')
    axes[2].set_title('Coarse Heatmap')
    axes[2].axis('off')
    plt.show()


# Example usage
image_paths = ["/mnt/d/Users/<USER>/FISH_spots/2d/image_raw/large spots/images/synthetic_000596.tif" , "/mnt/d/Users/<USER>/FISH_spots/2d/image_raw/large spots/images/synthetic_000597.tif"] # Replace with actual paths
mask_paths = ["/mnt/d/Users/<USER>/FISH_spots/2d/image_raw/large spots/masks/synthetic_000596.tif", "/mnt/d/Users/<USER>/FISH_spots/2d/image_raw/large spots/masks/synthetic_000597.tif"]  # Replace with actual paths


# Create an instance of the AdaptiveSpotDataset class
dataset = AdaptiveSpotDataset(image_paths, mask_paths)

# Test the heatmap generation
for idx in range(len(dataset)):
    img_t, msk_t, flow_t, confidence_t = dataset[idx]
    mask = msk_t[0].numpy()
    centroids, spot_sizes = dataset._get_centroids_and_sizes(mask)
    heatmaps = dataset._adaptive_heatmap_generation(mask, centroids, spot_sizes)

    visualize_mask_and_centroids(mask, centroids)
    visualize_heatmaps(heatmaps)

## TO RESUME TRAINING FROM A CHECKPOINT, 
## LOAD THE MODEL, THEN RUN THE TRAINING CELL AGAIN
## Using the cells below



# --- RESUME TRAINING FROM BEST CHECKPOINT IF EXISTS ---
import os
model = AdaptiveSpotDetector(in_ch=1, num_heatmaps=3, base_ch=32)
best_model_path = os.path.join(model_dir, 'spotmodel_claude7082025.pth')
if os.path.exists(best_model_path):
    print(f"Loading weights from {best_model_path}")
    model.load_state_dict(torch.load(best_model_path, map_location=device))
model.to(device)





# Create data loaders
train_loader, val_loader = create_data_loaders(
    image_paths, mask_paths, batch_size=32, patch_size=128
)

trained_model = notebook_train_model(
    model, train_loader, val_loader, num_epochs=350, device=device, model_dir=model_dir
)




Data Loader
The AdaptiveSpotDataset class is designed to load and preprocess images and their corresponding masks. Here's a breakdown of its functionality:

Image and Mask Loading:

Images and masks are loaded using tifffile.imread, and images are normalized to have pixel values between 0 and 1.
Masks are filtered to include only valid spot IDs and processed to extract centroids, sizes, and various forms of target representations.
Patch Extraction:

Large images are divided into smaller patches of a fixed size (patch_size), which helps manage memory and allows the model to focus on local features.
If a patch contains valid spots, it is centered around a randomly chosen spot.
Target Generation:

Several types of target representations are generated from the mask:
Semantic Mask: Indicates spot locations.
Boundary Mask: Identifies the boundaries of spots.
Distance Transform: Computes distances from spot boundaries.
Instance Mask: A binary mask for all spots.
Gaussian Heatmap: Generated around spot centroids to aid in spot detection.
Flow Field: Represents the direction towards the nearest spot centroid for each pixel.
Data Augmentation:

Augmentations such as rotation, flipping, brightness adjustments, and blurring are applied to increase model robustness.
Weighted Sampling:

A weighted sampler balances the dataset, ensuring patches with varying densities of spots are proportionally represented.
Batch Creation:

Data is batched using DataLoader, with options for shuffling and parallel data loading.
Model Architecture
The AdaptiveSpotDetector model is a deep convolutional neural network designed for spot detection, with elements relevant for instance segmentation:

Input Layer:

The model accepts grayscale images (single channel) as input.
Encoder:

Consists of several convolutional blocks (enc_block) with increasing depth.
Each block includes convolutional layers with batch normalization and ReLU activations, followed by max-pooling for downsampling.
Encoder blocks progressively reduce spatial dimensions and increase feature depth (e.g., from base_ch to base_ch*16).
Bottleneck:

Includes a sequence of convolutions with batch normalization and ReLU activations, followed by dropout.
Features a Squeeze-and-Excitation (SE) block for channel-wise attention and an Atrous Spatial Pyramid Pooling (ASPP) module to capture multi-scale context using dilated convolutions at different rates.
Decoder:

Uses transposed convolutions for upsampling, combined with skip connections from corresponding encoder layers to preserve spatial information.
The decoder progressively increases spatial resolution while reducing channel depth, integrating skip connections and applying dropout for regularization.
Output Heads:

Semantic Head: Predicts a binary segmentation map indicating spot locations.
Boundary Head: Predicts boundaries of spots.
Distance Head: Predicts the distance to the nearest spot boundary.
Heatmap Head: Generates heatmaps for spot detection.
Flow Head: Predicts a flow field indicating directions to spot centroids, useful for associating pixels with particular spots.
Loss Function
The AdaptiveSpotLoss class combines several loss terms to guide the training process effectively:

Semantic Loss:

Combines focal loss and Dice loss to train the semantic segmentation head, focusing on accurately identifying spot locations.
Boundary Loss:

Similar to semantic loss but applied to boundary detection, helping the model accurately delineate the edges of spots.
Distance Loss:

Mean squared error (MSE) loss is used for distance transform predictions, ensuring accurate distance estimations from spot boundaries.
Instance Loss:

Focal and Dice loss applied to the instance mask prediction, aiding in accurately segmenting individual spots.
Heatmap Loss:

Combines focal loss, Dice loss, and MSE for Gaussian heatmap prediction, ensuring accurate spot localization.
Flow Loss:

MSE loss applied to the flow field prediction, ensuring predicted flow vectors accurately point towards spot centroids.
Deep Supervision Loss:

Additional focal loss applied to intermediate layers (deep supervision) to improve gradient flow and learning in deeper layers.
Each loss term is weighted and combined into a single scalar loss value, which is minimized during training.

Prediction and Post-Processing
After training, the model can predict spots in new images through the following steps:

Model Inference:

The model generates multiple output maps for a given input image, including semantic masks, boundary masks, distance maps, heatmaps, and flow fields.
Spot Extraction:

The extract_precise_spots function processes the predicted Gaussian heatmap and semantic mask to identify potential spot locations using peak detection algorithms.
Non-Maximum Suppression (NMS):

Overlapping or closely located spots might be detected as multiple candidates. NMS is used to filter out redundant detections, retaining only the most confident ones.
In inference_with_nms, spots are sorted by confidence and filtered based on a distance threshold to retain only distinct spots.
Evaluation:

During evaluation, metrics such as precision, recall, and F1 score are computed by comparing predicted spots with ground truth annotations.
Visualization:

Results can be visualized by overlaying detected spots on the input image, showing segmentation masks, and displaying heatmaps or flow fields as needed for interpretation.
Summary
The pipeline is comprehensive and tailored for accurate spot detection and segmentation in images. It includes:

A robust data loader for efficient patch extraction, augmentation, and weighted sampling.
A complex model architecture with encoder-decoder design and multiple output heads tailored for spot detection and segmentation tasks.
A composite loss function that combines multiple terms, ensuring balanced learning across different tasks.
Post-processing steps that include spot extraction and non-maximum suppression to refine detection results and evaluate performance using standard metrics.